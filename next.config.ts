import type { NextConfig } from 'next';

const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
});

const isLocal = process.env.NEXT_PUBLIC_DOMAIN === 'localhost';

const withWorkbox = isLocal
  ? (config: NextConfig) => config
  : require('next-with-workbox');

const nextConfig: NextConfig = {
  experimental: {
    reactCompiler: false,
  },
  assetPrefix: process.env.ASSET_PREFIX,
  logging: {
    fetches: {
      fullUrl: true,
    },
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    formats: ['image/webp'],
    minimumCacheTTL: 60 * 60 * 24, // 1day
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'storage.googleapis.com',
        port: '',
        pathname: '/lobox_public_images/**',
      },
      {
        protocol: 'https',
        hostname: 'cdn.lobox.com',
        port: '',
        pathname: '/assets/images/**',
      },
      {
        protocol: 'https',
        hostname: 'cdn.lobox.com',
        port: '',
        pathname: '/image/**',
      },
      {
        protocol: 'https',
        hostname: 'storage.googleapis.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
  reactStrictMode: false,
  webpack: (config) => {
    config.resolve.alias.canvas = false;
    return config;
  },
};

module.exports = withBundleAnalyzer(
  withWorkbox({
    workbox: {
      dest: 'public',
      swDest: 'sw.js',
      swSrc: 'service-worker.ts',
      force: true,
    },
    ...nextConfig,
  })
);
