/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: 'class',
  content: ['./src/**/*.{js,ts,jsx,tsx,mdx}'],
  theme: {
    extend: {
      colors: {
        /* Brand Colors */
        brand: 'var(--brand)',
        brand_4: 'var(--brand_4)',
        brand_10: 'var(--brand_10)',
        busbrand_10: 'var(--busbrand_10)',
        brand_20: 'var(--brand-20)',
        semi_brand: 'var(--semi-brand)',
        darkBrand: 'var(--darkBrand)',
        darkBrandHover: 'var(--darkBrandHover)',
        trench: 'var(--trench)',
        popOverBg: 'var(--popOverBg)',

        /* Theme Colors */
        smoke_coal: 'var(--smoke_coal)',
        primaryText: 'var(--primaryText)',
        secondaryDisabledText: 'var(--secondaryDisabledText)',
        popOverBg_white: 'var(--popOverBg_white)',
        darkBackground_background2light:
          'var(--darkBackground-background2light)',
        colorIconForth2: 'var(--colorIconForth2)',
        darkSecondary_hover: 'var(--darkSecondary_hover)',
        hoverPrimary: 'var(--hoverPrimary)',
        graphene60_gray60: 'var(--graphene60-gray60)',
        gray_5: 'var(--gray_5)',
        gray: 'var(--gray)',
        skeletonBg: 'var(--skeletonBg)',
        white: 'var(--white)',

        /* Additional Colors */
        success: 'var(--success)',
        success_10: 'var(--success_10)',
        success_20: 'var(--success_20)',
        error: 'var(--error)',
        error_5: 'var(--error_5)',
        pendingOrange: 'var(--pendingOrange)',
        pendingOrange_10: 'var(--pendingOrange_10)',
        pendingOrange_20: 'var(--pendingOrange_20)',
        gold: 'var(--gold)',
        goldHover: 'var(--goldHover)',
        brownish: 'var(--brownish)',
        brown: 'var(--brown)',
        cornflowerBlue: 'var(--cornflowerBlue)',
        heliotrope: 'var(--heliotrope)',
        darkTangerine: 'var(--darkTangerine)',
        darkError: 'var(--darkError)',
        lightestGreen: 'var(--lightestGreen)',
        techGray_10: 'var(--techGray_10)',
        techGray_20: 'var(--techGray_20)',
        like_bg: 'var(--like-bg)',
        booster: 'var(--booster)',
        tooltipText: 'var(--tooltipText)',
        celeb_bg_dark_light: 'var(--celeb-bg-dark-light)',
        background2_dark_light: 'var(--background2-dark-light)',
        ban_bg: 'var(--ban-bg)',
        little_transparent_background: 'var(--little-transparent-background)',
        white_80: 'var(--white_80)',
        white_60: 'var(--white_60)',
        pale_background: 'var(--pale-background)',
        background: 'var(--background)',
        glassEffect_50: 'var(--glassEffect_50)',
        glassEffect_70: 'var(--glassEffect_70)',
        warning_10: 'var(--warning_10)',
        modalBlur: 'var(--modalBlur)',
      },
      screens: {
        sm: '1124px',
      },
      spacing: {
        0: '0px',
        1: '1px',
        2: '2px',
        3: '3px',
        4: '4px',
        6: '6px',
        8: '8px',
        10: '10px',
        12: '12px',
        16: '16px',
        20: '20px',
        22: '22px',
        24: '24px',
        26: '26px',
        32: '32px',
        40: '40px',
        48: '48px',
        '8_12': 'var(--8_12)',
        '24_32': 'var(--24_32)',
        '16_20': 'var(--16_20)',
        '12_16': 'var(--12_16)',
        '16_8': 'var(--16_8)',
        '0_20': 'var(--0_20)',
        '0_4': 'var(--0_4)',
        '16_0': 'var(--16_0)',
        '16_12': 'var(--16_12)',
        'right-side-big': '609px',
      },
    },
  },
  plugins: [],
};
