import type { RatingProps as MuiRatingProps } from '@mui/material';
import { Rating as MuiRating } from '@mui/material';
import Icon from 'shared/uikit/Icon';
import cnj from 'shared/uikit/utils/cnj';
import type { FC } from 'react';
import classes from './index.module.scss';

interface RatingProps extends Omit<MuiRatingProps, 'size'> {
  spacing?: number;
  size: number;
}

const Rating: FC<RatingProps> = (props) => {
  const { spacing = 0, size = 16, ...rest } = props;

  return (
    <MuiRating
      {...rest}
      style={
        { ...rest.style, '--spacing': `${spacing}px` } as React.CSSProperties
      }
      icon={<Icon type="far" name="star-s" size={size} color="darkTangerine" />}
      emptyIcon={
        <Icon
          type="far"
          name="star"
          size={size}
          color={Number(rest.value) === 0 ? 'smoke_coal' : 'darkTangerine'}
        />
      }
      precision={rest.precision ?? 1}
      classes={{
        ...rest.classes,
        root: cnj(classes.root, rest.classes?.root),
      }}
    />
  );
};

export default Rating;
