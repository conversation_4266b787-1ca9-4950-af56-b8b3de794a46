import Button from '@shared/uikit/Button';
import useOpenConfirm from '@shared/uikit/Confirmation/useOpenConfirm';
import Flex from '@shared/uikit/Flex';
import Form from '@shared/uikit/Form';
import DynamicFormBuilder from '@shared/uikit/Form/DynamicFormBuilder';
import SubmitButton from '@shared/uikit/Form/SubmitButton';
import FixedRightSideModalDialog from '@shared/uikit/Modal/FixedRightSideModalDialog/FixedRightSideModalDialog.component';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import ModalFooter from '@shared/uikit/Modal/ModalFooter';
import ModalHeaderSimple from '@shared/uikit/Modal/ModalHeaderSimple';
import Typography from '@shared/uikit/Typography';
import cnj from '@shared/uikit/utils/cnj';
import useTheme from '@shared/uikit/utils/useTheme';
import formValidator from '@shared/utils/form/formValidator';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { FormikProps } from 'formik';
import {
  type UIEventHandler,
  type FC,
  useState,
  useCallback,
  useMemo,
} from 'react';
import useToast from '@shared/uikit/Toast/useToast';
import Carousel from '@shared/uikit/Carousel';
import throttle from 'lodash/throttle';
import classes from './HorizontalTagList.module.scss';

interface TagsFormType {
  tags: string[];
}

export interface HorizontalTagListProps extends Partial<TagsFormType> {
  title?: string;
  hideEmpty?: true;
  editable?: boolean;
  maxTags?: number;
  local?: boolean;
  apiFunc?: (data: TagsFormType) => Promise<any>;
  onSuccess?: (data: TagsFormType) => void;
  className?: string;
  areaClassName?: string;
}

const HorizontalTagList: FC<HorizontalTagListProps> = ({
  title,
  tags = [],
  hideEmpty,
  editable,
  maxTags,
  local,
  apiFunc,
  onSuccess,
  className,
  areaClassName,
}) => {
  const { openConfirmDialog } = useOpenConfirm({
    variant: 'wideRightSideModal',
  });
  const [isOpen, setIsOpen] = useState(false);
  const { isDark } = useTheme();
  const { t } = useTranslation();
  const [pos, setPos] = useState({
    isRight: false,
    isLeft: true,
  });
  const toast = useToast();

  const onAlert = async (
    message: string,
    type: 'success' | 'error',
    alertTitle = 'candidate_tags'
  ) => {
    toast({
      type,
      icon: `${type === 'success' ? 'check' : 'times'}-circle`,
      title: type === 'error' ? t('error') : t(alertTitle),
      message: t(message),
    });
  };

  const onError = (error: any) => {
    let message = error?.response?.data?.defaultMessage ?? 'Error!';
    if (error?.response?.data?.fieldErrors?.length > 0) {
      message = `${error.response.data.fieldErrors[0].field}: ${error.response.data.fieldErrors[0].defaultMessage}`;
    }

    onAlert(message, 'error');
  };

  const handleOnClose = useCallback(
    ({ dirty, resetForm }: FormikProps<TagsFormType>) => {
      function close() {
        resetForm();
        setIsOpen(false);
      }
      if (dirty) {
        openConfirmDialog({
          title: t('confirm_title'),
          message: t('confirm_desc'),
          cancelButtonText: t('confirm_cancel'),
          confirmButtonText: t('confirm_ok'),
          cancelCallback: close,
          isReverse: true,
        });
      } else {
        close();
      }
    },
    [openConfirmDialog, t]
  );
  const handleOnSuccess = (data: TagsFormType) => {
    onSuccess?.(data);
    onAlert('success_updated', 'success');
    setIsOpen(false);
  };

  const validationSchema = useMemo(() => {
    let tagsValidator = formValidator.array().of(formValidator.string());
    if (maxTags) {
      tagsValidator = tagsValidator.max(maxTags);
    }
    return formValidator.object().shape({
      tags: tagsValidator,
    });
  }, [maxTags]);

  const getScrollPosition = useCallback<UIEventHandler>(
    throttle(
      (ev) => {
        const { width } = ev.target.getBoundingClientRect();
        const { scrollWidth, scrollLeft } = ev.target;

        setPos({
          isRight: scrollLeft + width >= scrollWidth - 20,
          isLeft: scrollLeft <= 20,
        });
      },
      500,
      { leading: true }
    ),
    [setPos]
  );

  const openEditor = () => {
    if (editable) {
      setIsOpen(true);
    }
  };

  if (hideEmpty && !tags?.length) return null;

  return (
    <Flex className={cnj(className, editable ? 'cursor-pointer' : undefined)}>
      <Carousel
        showCenterButtons
        childrenWrapperClassName="gap-8"
        moveWalkDistance={400}
        buttonVariant="rectangle"
        className="relative"
        areaClassName={areaClassName}
        rightBtnClassName={cnj(
          classes.navButton,
          isDark ? classes.isDark : undefined,
          classes.navRight,
          pos?.isRight ? classes.hide : undefined
        )}
        leftBtnClassName={cnj(
          classes.navButton,
          isDark ? classes.isDark : undefined,
          classes.navLeft,
          pos?.isLeft ? classes.hide : undefined
        )}
        onScroll={getScrollPosition}
      >
        {tags.length ? (
          tags.map((tag) => (
            <Typography
              key={tag}
              className="py-4 px-8 rounded bg-darkSecondary_hover text-nowrap select-none"
              onClick={openEditor}
            >
              {tag}
            </Typography>
          ))
        ) : (
          <Typography className="py-4 px-8 rounded bg-darkSecondary_hover text-nowrap select-none opacity-30">
            {t('no_tags')}
          </Typography>
        )}
        {editable ? (
          <Button
            label={t('add_tag')}
            labelFont="bold"
            schema="ghost-brand"
            onClick={() => setIsOpen(true)}
          />
        ) : null}
      </Carousel>
      <Form
        local={local}
        initialValues={{ tags }}
        onSuccess={handleOnSuccess}
        onFailure={onError}
        apiFunc={apiFunc}
        enableReinitialize
        validationSchema={validationSchema}
      >
        {(formik) => (
          <FixedRightSideModalDialog
            visibleBackdrop={false}
            wide
            isOpen={isOpen}
            onClose={() => handleOnClose(formik)}
            onBack={() => handleOnClose(formik)}
            onClickOutside={() => handleOnClose(formik)}
          >
            <ModalHeaderSimple
              visibleHeaderDivider
              backButtonProps={{
                onClick: () => handleOnClose(formik),
              }}
              hideBack
              noCloseButton={false}
              title={title ?? t('add_tags')}
              belowContent={
                <Flex className={classes.formSubTitle}>
                  <Typography
                    size={16}
                    height={18}
                    font="700"
                    color="primaryText"
                  >
                    {t('tags')}
                  </Typography>
                </Flex>
              }
            />
            <ModalBody>
              <DynamicFormBuilder
                groups={[
                  {
                    name: 'tags',
                    cp: 'inputTags',
                    label: t('add_tags'),
                    required: true,
                    useValidate: false,
                    maxTags,
                  },
                ]}
              />
            </ModalBody>
            <ModalFooter>
              <Flex flexDir="row" className={classes.footerButtons}>
                <Button
                  fullWidth
                  schema="secondary-dark"
                  onClick={() => handleOnClose(formik)}
                  label={t('discard')}
                />
                <SubmitButton fullWidth label={t('save')} />
              </Flex>
            </ModalFooter>
          </FixedRightSideModalDialog>
        )}
      </Form>
    </Flex>
  );
};

export default HorizontalTagList;
