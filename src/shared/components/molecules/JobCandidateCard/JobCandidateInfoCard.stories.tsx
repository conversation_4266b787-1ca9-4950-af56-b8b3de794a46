import type { ArgTypes, Meta, StoryObj } from '@storybook/react';
import { CandidateDataSamples } from '@shared/data-samples';
import { fn } from '@storybook/test';
import type { JobCandidateInfoCardProps } from './JobCandidateInfoCard';
import JobCandidateInfoCardComponent from './JobCandidateInfoCard';
import CardWrapper from '../CardItem/CardWrapper';
import JobCandidateInfoCardSkeleton from './JobCandidateInfoCard.skeleton';

const meta: Meta<JobCandidateInfoCardProps> = {
  title: 'Portals/Lobox Recruiter/Candidate/JobCandidateInfoCard',
  component: JobCandidateInfoCardComponent,
  subcomponents: { CardWrapper, JobCandidateInfoCardSkeleton },
};

export default meta;
type Story = StoryObj<typeof meta>;

const defaultProps: Partial<JobCandidateInfoCardProps> = {
  candidate: CandidateDataSamples.candidate1,
};

const defaultArgTypes: Partial<ArgTypes<JobCandidateInfoCardProps>> = {
  candidate: {
    control: false,
  },
  classNames: {
    control: false,
  },
};

export const Main: Story = {
  args: defaultProps,
  argTypes: defaultArgTypes,
};

export const Skeleton: Story = {
  render: (args) => <JobCandidateInfoCardSkeleton className="!w-[337px]" />,
  args: defaultProps,
  argTypes: defaultArgTypes,
};

export const WithCardWrapper: Story = {
  render({ onClick, isFocused, ...args }) {
    return (
      <CardWrapper onClick={onClick} isFocused={isFocused}>
        <JobCandidateInfoCardComponent {...args} />
      </CardWrapper>
    );
  },
  args: { ...defaultProps, onClick: fn() },
  argTypes: {
    ...defaultArgTypes,
    isFocused: {
      control: 'boolean',
    },
  },
};
