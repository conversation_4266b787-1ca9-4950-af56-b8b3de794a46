import type { CandidateFormData } from '@shared/types/candidates';
import applyResolutionToImageSrc from '@shared/utils/toolkit/applyResolutionToImageSrc';
import cleanRepeatedWords from '@shared/utils/toolkit/cleanRepeatedWords';
import type { ComponentProps, FC } from 'react';
import { memo, useMemo } from 'react';
import ObjectInfoCard from '../ObjectInfoCard';

export interface JobCandidateInfoCardProps
  extends ComponentProps<typeof ObjectInfoCard> {
  classNames?: Partial<{
    root: string;
    header: string;
    avatar: string;
    infoCard: string;
    actions: string;
  }>;
  actions?: ReactNode;
  candidate: CandidateFormData;
}

const JobCandidateInfoCard: FC<JobCandidateInfoCardProps> = (props) => {
  const { classNames, candidate, actions, ...objectInfoProps } = props;
  const { profile = {} as any } = candidate;
  const {
    croppedImageUrl,
    location,
    usernameAtSign,
    fullName,
    occupation,
    email,
  } = profile;

  const image = useMemo(
    () =>
      croppedImageUrl
        ? applyResolutionToImageSrc(croppedImageUrl, 'small')
        : undefined,
    [croppedImageUrl]
  );

  return (
    <ObjectInfoCard
      withAvatar
      className="w-full items-center gap-10"
      avatar={image}
      avatarProps={{
        name: fullName,
        className: classNames?.avatar,
        isCompany: false,
        size: 'flg',
      }}
      actions={actions}
      firstText={fullName}
      secondText={usernameAtSign ?? email?.value}
      thirdText={occupation?.label}
      fourthText={cleanRepeatedWords(location?.title || '')}
      isSecondTextSmall
      {...objectInfoProps}
    />
  );
};

export default memo(JobCandidateInfoCard);
