import Flex from '@shared/uikit/Flex';
import Skeleton from '@shared/uikit/Skeleton';
import cnj from '@shared/uikit/utils/cnj';
import type { FC } from 'react';

const JobCandidateInfoCardSkeleton: FC<{
  className?: string;
}> = ({ className }) => (
  <Flex
    flexDir="row"
    className={cnj('w-full items-center gap-10 !h-[86px]', className)}
  >
    <Skeleton className="rounded-full !size-[80px]" />
    <Flex className="gap-4 flex-1">
      {Array.from({ length: 4 }).map((_, index) => (
        <Skeleton key={index} className="h-16 rounded" />
      ))}
    </Flex>
  </Flex>
);

export default JobCandidateInfoCardSkeleton;
