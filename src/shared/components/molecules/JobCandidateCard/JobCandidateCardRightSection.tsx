import type { JobCandidateCardInfoCardProps } from '@shared/components/molecules/JobCandidateCard/types';
import InfoCard from '@shared/components/Organism/Objects/Common/InfoCard';
import Flex from '@shared/uikit/Flex';

const JobCandidateCardRightSection = ({
  items,
}: {
  items: JobCandidateCardInfoCardProps[];
}) => {
  return (
    <Flex className="bg-popOverBg justify-center items-start p-12 gap-4 w-[40%] relative rounded-r-xl">
      <Flex className="bg-brand_4 absolute  top-0 left-0 w-full h-full rounded-r-xl" />
      {items.map(({ key, title, value, icon }) => (
        <InfoCard
          key={key}
          title={title}
          titleProps={{
            fontWeight: 500,
            size: 12,
            color: 'muteMidGray',
          }}
          icon={icon}
          value={value}
          className="w-full"
          wrapperClassName="!bg-transparent"
          iconClassName="!rounded-md"
        />
      ))}
    </Flex>
  );
};

export default JobCandidateCardRightSection;
