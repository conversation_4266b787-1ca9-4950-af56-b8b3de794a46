import type { IconName } from '@shared/uikit/Icon';

export interface OtherProps {
  variant: 'applicant' | 'candidate';
  onChangePipeline: (pipeline: string) => void;
  onPrimaryButtonClick: () => void;
}

interface CandidateCardClassNamesProps {
  root?: string;
  header?: string;
  avatar?: string;
  infoCard?: string;
}

export interface JobCandidateCardProps {
  onClick?: VoidFunction;
  isFocused?: boolean;
  classNames?: CandidateCardClassNamesProps;
  footer?: React.ReactNode;
  showTags?: boolean;
  showIsManual?: boolean;
  enableLinks?: boolean;
  showActions?: boolean;
}

export interface JobCandidateCardInfoCardProps {
  key: string;
  title: string;
  value: string | number | undefined;
  icon: IconName;
}
