import CardWrapper from '@shared/components/molecules/CardItem/CardWrapper';
import Flex from '@shared/uikit/Flex';
import Skeleton from '@shared/uikit/Skeleton';
import cnj from '@shared/uikit/utils/cnj';
import type { FC, PropsWithChildren } from 'react';
import classes from '../BusinessJobCard/BusinessJobCard.module.scss';
import canClasses from './JobCandidateCard.module.scss';
import CandidateInfoCardSkeleton from './JobCandidateInfoCard.skeleton';

interface CandidateCardSkeletonProps {
  className?: string;
  footer?: React.ReactNode;
  showTags?: true;
  showBadges?: true;
}

const CandidateCardSkeleton: FC<
  PropsWithChildren<CandidateCardSkeletonProps>
> = ({ className, footer, children, showTags, showBadges }) => (
  <CardWrapper
    classNames={{
      root: cnj(canClasses.root, className),
      container: cnj(classes.container, canClasses.container),
    }}
    bottomComponents={footer}
  >
    <CandidateInfoCardSkeleton />
    {showTags ? (
      <Flex className={classes.badges}>
        {Array.from({ length: 3 }).map((_, index) => (
          <Skeleton key={index} className="!w-[100px] h-32 rounded" />
        ))}
        <Skeleton className="!w-14 h-16 rounded" />
      </Flex>
    ) : null}
    {showBadges ? (
      <Flex className={classes.badges}>
        {Array.from({ length: 3 }).map((_, index) => (
          <Skeleton key={index} className="!w-[48px] h-26 rounded" />
        ))}
        <Skeleton className="!w-14 h-16 rounded ml-auto" />
      </Flex>
    ) : null}
    {children}
  </CardWrapper>
);

export default CandidateCardSkeleton;
