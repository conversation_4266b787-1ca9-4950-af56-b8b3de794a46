import type { ArgTypes, Meta, StoryObj } from '@storybook/react';
import { CandidateDataSamples } from '@shared/data-samples';
import { fn } from '@storybook/test';
import type { JobCandidateCardProps } from '@shared/components/molecules/JobCandidateCard/types';
import JobCandidateCardComponent from './JobCandidateCard';
import JobCandidateCardSkeleton from './JobCandidateCardSkeleton';

const meta: Meta<JobCandidateCardProps> = {
  title: 'Portals/Lobox Recruiter/Candidate/JobCandidateCard',
  component: JobCandidateCardComponent,
  subcomponents: { JobCandidateCardSkeleton },
};

export default meta;
type Story = StoryObj<typeof meta>;

const defaultProps: Partial<JobCandidateCardProps> = {
  candidate: CandidateDataSamples.candidate1,
  onClick: fn(),
  isFocused: false,
  showActions: true,
  showTags: true,
  showBadges: true,
  showIsManual: true,
  enableLinks: true,
  classNames: {
    root: '!w-[600px]',
  },
};

const defaultArgTypes: Partial<ArgTypes<JobCandidateCardProps>> = {
  candidate: {
    control: false,
  },
  classNames: {
    control: false,
  },
};

export const Main: Story = {
  args: defaultProps,
  argTypes: defaultArgTypes,
};

export const Skeleton: Story = {
  render: (args) => (
    <JobCandidateCardSkeleton {...args} className={args.classNames?.root} />
  ),
  args: defaultProps,
  argTypes: defaultArgTypes,
};
