import { type FC } from 'react';
import SectionLayout from 'shared/components/Organism/Objects/Common/Section.layout';
import cnj from 'shared/uikit/utils/cnj';
import Skeleton from '@shared/uikit/Skeleton';
import SearchCardSkeleton from '@shared/components/Organism/SearchCard/SearchCardSkeleton';
import type { JobFullDetailsClassNameProps } from '@shared/components/molecules/Job/types';
import classes from './JobStyles.module.scss';

const JobPointOfContantSkeleton: FC<JobFullDetailsClassNameProps> = ({
  classNames,
}) => (
  <SectionLayout
    title={<Skeleton className="!w-[80px] h-24 rounded" />}
    classNames={{
      childrenWrap: cnj(
        classes.root,
        classes.noPadding,
        classNames?.sectionLayout
      ),
    }}
  >
    <SearchCardSkeleton />
  </SectionLayout>
);

export default JobPointOfContantSkeleton;
