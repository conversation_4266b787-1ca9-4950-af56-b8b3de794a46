import useTranslation from 'shared/utils/hooks/useTranslation';
import type { JobProps } from 'shared/types/jobsProps';
import type { FC } from 'react';
import SectionLayout from 'shared/components/Organism/Objects/Common/Section.layout';
import cnj from 'shared/uikit/utils/cnj';
import type { JobFullDetailsClassNameProps } from '@shared/components/molecules/Job/types';
import JobPreviewInfoItem from './JobPreviewInfoItem';
import classes from './JobStyles.module.scss';

interface JobRequirementsProps extends JobFullDetailsClassNameProps {
  job: JobProps;
}

const JobRequirements: FC<JobRequirementsProps> = ({ job, classNames }) => {
  const { t } = useTranslation();
  return (
    <SectionLayout
      title={t('requirements')}
      classNames={{
        childrenWrap: cnj(classes.root, classNames?.sectionLayout),
      }}
    >
      {!!job.experienceLevel && (
        <JobPreviewInfoItem
          icon="signal-bar"
          title={t('exp_level')}
          subTitle={t(job.experienceLevel)}
          noRadius={false}
        />
      )}
      {true && (
        <JobPreviewInfoItem
          icon="graduation"
          title={t('degree')}
          subTitle={t('MASTER')}
        />
      )}
      {job.workDays?.length && (
        <JobPreviewInfoItem
          icon="calendar-days"
          title={t('working_days_in_a_week')}
          subTitle={job.workDays.length.toString()}
        />
      )}
      {job.hoursPerWeek && (
        <JobPreviewInfoItem
          icon="hourglass"
          title={t('required_hours_week')}
          subTitle={job.hoursPerWeek.toString()}
        />
      )}
      {!!job.workAuthorizations?.length &&
        job.workAuthorizations.map((auth) => (
          <JobPreviewInfoItem
            key={`job_id${job.id}_work_auth${auth.id}`}
            icon="user-document"
            title={t('work_authorization')}
            subTitle={`${auth.title}, ${auth.countryCode}`}
          />
        ))}
    </SectionLayout>
  );
};

export default JobRequirements;
