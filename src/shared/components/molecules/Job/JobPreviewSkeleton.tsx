import type { FC } from 'react';
import SectionLayout from 'shared/components/Organism/Objects/Common/Section.layout';
import cnj from 'shared/uikit/utils/cnj';
import Skeleton from '@shared/uikit/Skeleton';
import type { JobFullDetailsClassNameProps } from '@shared/components/molecules/Job/types';
import classes from './JobStyles.module.scss';
import JobPreviewInfoItemSkeleton from './JobPreviewInfoItemSkeleton';

const JobPreviewSkeleton: FC<
  JobFullDetailsClassNameProps & { itemsCount?: number }
> = (props) => {
  const { classNames, itemsCount = 11 } = props;
  return (
    <SectionLayout
      title={<Skeleton className="!w-[80px] h-24 rounded" />}
      classNames={{
        childrenWrap: cnj(classes.root, classNames?.sectionLayout),
      }}
    >
      {new Array(itemsCount).fill(1).map((_, i) => (
        <JobPreviewInfoItemSkeleton key={`job_preview_item_${i}`} />
      ))}
    </SectionLayout>
  );
};

export default JobPreviewSkeleton;
