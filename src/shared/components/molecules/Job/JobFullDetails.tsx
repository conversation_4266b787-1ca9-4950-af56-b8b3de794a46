import type { JobProps } from 'shared/types/jobsProps';
import type { FC } from 'react';
import EntityId from '@shared/uikit/EntityId';
import { type JobFullDetailsClassNameProps } from '@shared/components/molecules/Job/types';
import JobPreview from './JobPreview';
import JobDetails from './JobDetails';
import JobSkills from './JobSkills';
import JobLanguages from './JobLanguages';
import JobRequirements from './JobRequirements';
import JobBenefits from './JobBenefits';
import JobPointOfContant from './JobPointOfContant';

interface JobFullDetailsProps extends JobFullDetailsClassNameProps {
  job: JobProps;
}

const JobFullDetails: FC<JobFullDetailsProps> = ({ job, classNames }) => (
  <>
    <JobPreview job={job} classNames={classNames} />
    <JobDetails
      description={job.description}
      hashtags={job.hashtags}
      classNames={classNames}
    />
    <JobSkills skills={job.skills} classNames={classNames} />
    {!!job.languages?.length && (
      <JobLanguages languages={job.languages} classNames={classNames} />
    )}
    <JobRequirements job={job} classNames={classNames} />
    {!!job.benefits?.length && (
      <JobBenefits benefits={job.benefits} classNames={classNames} />
    )}
    <JobPointOfContant
      collaborators={job.collaborators}
      classNames={classNames}
    />
    <EntityId
      id={job.id}
      name="job_id"
      alertText="t_j_l_h_b_c_t_y_c"
      type="job"
    />
  </>
);

export default JobFullDetails;
