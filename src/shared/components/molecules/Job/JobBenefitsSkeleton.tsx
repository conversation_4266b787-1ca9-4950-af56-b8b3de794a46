import Flex from 'shared/uikit/Flex';
import { type FC } from 'react';
import SectionLayout from 'shared/components/Organism/Objects/Common/Section.layout';
import cnj from 'shared/uikit/utils/cnj';
import Skeleton from '@shared/uikit/Skeleton';
import type { JobFullDetailsClassNameProps } from '@shared/components/molecules/Job/types';
import classes from './JobStyles.module.scss';

const JobBenefitsSkeleton: FC<JobFullDetailsClassNameProps> = ({
  classNames,
}) => (
  <SectionLayout
    title={<Skeleton className="!w-[80px] h-24 rounded" />}
    classNames={{
      childrenWrap: cnj(classes.root, classNames?.sectionLayout),
    }}
  >
    <Flex className={classes.benefitsRoot}>
      {new Array(5).fill(1).map((_, i) => (
        <Flex
          className="!flex-row gap-12 items-center"
          key={`job_preview_item_${i}`}
        >
          <Skeleton className="h-24 !w-24 rounded-full" />
          <Skeleton className="h-16 !w-[80px] rounded" />
        </Flex>
      ))}
    </Flex>
  </SectionLayout>
);

export default JobBenefitsSkeleton;
