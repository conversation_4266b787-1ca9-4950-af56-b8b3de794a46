import Flex from 'shared/uikit/Flex';
import type { FC } from 'react';
import SectionLayout from 'shared/components/Organism/Objects/Common/Section.layout';
import cnj from 'shared/uikit/utils/cnj';
import useTranslation from 'shared/utils/hooks/useTranslation';
import type { JobFullDetailsClassNameProps } from '@shared/components/molecules/Job/types';
import classes from './JobStyles.module.scss';
import JobPreviewInfoItemSkeleton from './JobPreviewInfoItemSkeleton';
import Skeleton from '@shared/uikit/Skeleton';

const JobDetailsSkeleton: FC<JobFullDetailsClassNameProps> = ({
  classNames,
}) => {
  const { t } = useTranslation();
  return (
    <SectionLayout
      title={<Skeleton className="!w-[80px] h-24 rounded" />}
      classNames={{
        childrenWrap: cnj(classes.root, classNames?.sectionLayout),
      }}
    >
      <Flex className="gap-16">
        <Skeleton className="h-[50px] rounded" />
        <Skeleton className="h-[25px] rounded" />
        <Skeleton className="h-[125px] rounded" />
        <Skeleton className="h-[250px] rounded" />
        <Skeleton className="h-[100px] rounded" />
      </Flex>
      <JobPreviewInfoItemSkeleton />
    </SectionLayout>
  );
};

export default JobDetailsSkeleton;
