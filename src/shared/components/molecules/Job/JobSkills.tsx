import type { FC } from 'react';
import SectionLayout from 'shared/components/Organism/Objects/Common/Section.layout';
import {
  type JobSkillProps,
  type SkillLevelType,
} from 'shared/types/jobsProps';
import ProgressItem from 'shared/uikit/ProgressItem';
import cnj from 'shared/uikit/utils/cnj';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { type JobFullDetailsClassNameProps } from '@shared/components/molecules/Job/types';
import classes from './JobStyles.module.scss';

interface JobSkillsProps extends JobFullDetailsClassNameProps {
  skills: JobSkillProps[];
}

const JobSkills: FC<JobSkillsProps> = ({ skills, classNames }) => {
  const { t } = useTranslation();

  return (
    <SectionLayout
      title={t('skills')}
      classNames={{
        childrenWrap: cnj(classes.root, classNames?.sectionLayout),
      }}
    >
      {skills.map((skill) => (
        <ProgressItem
          key={`skill_${skill.skillId}`}
          title={skill.skillName}
          progressValue={skillLevelProgress[skill.skillLevel]}
          progressSteps={4}
          tooltipText={t(skill.skillLevel)}
        />
      ))}
    </SectionLayout>
  );
};

export default JobSkills;

const skillLevelProgress: { [key in SkillLevelType]: number } = {
  BEGINNER: 1,
  INTERMEDIATE: 2,
  ADVANCED: 3,
  EXPERT: 4,
};
