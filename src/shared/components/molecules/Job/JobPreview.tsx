import useTranslation from 'shared/utils/hooks/useTranslation';
import type { JobProps } from 'shared/types/jobsProps';
import type { FC } from 'react';
import SectionLayout from 'shared/components/Organism/Objects/Common/Section.layout';
import cnj from 'shared/uikit/utils/cnj';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import useShowCurrency from '@shared/hooks/useShowCurrency';
import type { JobFullDetailsClassNameProps } from '@shared/components/molecules/Job/types';
import classes from './JobStyles.module.scss';
import JobPreviewInfoItem from './JobPreviewInfoItem';

interface JobPreviewProps extends JobFullDetailsClassNameProps {
  job: JobProps;
}

const JobPreview: FC<JobPreviewProps> = ({ job, classNames }) => {
  const { t } = useTranslation();
  const { show } = useShowCurrency();

  return (
    <SectionLayout
      title={t('overview')}
      classNames={{
        childrenWrap: cnj(classes.root, classNames?.sectionLayout),
      }}
    >
      <JobPreviewInfoItem
        icon="category"
        title={t('Category')}
        subTitle={job.categoryName}
        noRadius={false}
      />
      <JobPreviewInfoItem
        icon="job-model"
        title={t('workspace_type')}
        subTitle={t(job.workPlaceType)}
      />
      <JobPreviewInfoItem
        icon="job"
        title={t('job_type')}
        subTitle={t(job.employmentType)}
      />
      {!!job.responseTime && (
        <JobPreviewInfoItem
          icon="response-time"
          title={t('response_time')}
          subTitle={t(job.responseTime.toLowerCase())}
        />
      )}
      {!!job.numberOfHires && (
        <JobPreviewInfoItem
          icon="group-position"
          title={t('number_of_hires')}
          subTitle={job.numberOfHires.toString()}
        />
      )}
      {!!(job.rangeMax || job.rangeMin) && (
        <JobPreviewInfoItem
          icon="salary-range"
          title={`${t('salary_range')}${job.period ? `(${t(job.period)})` : ''}`}
          subTitle={`${show({ currencyCode: job.currencyCode ?? 'USD', num: job.rangeMin ?? 0 })} - ${show({ currencyCode: job.currencyCode ?? 'USD', num: job.rangeMax ?? 0 })}`}
        />
      )}
      {!!job.taxTermName && (
        <JobPreviewInfoItem
          icon="calculator"
          title={t('tax_term')}
          subTitle={job.taxTermName}
        />
      )}
      {!!job.markup && (
        <JobPreviewInfoItem
          icon="money-increase"
          title={t('markup')}
          subTitle={String(job.markup)}
        />
      )}
      {!!job.contractDuration && (
        <JobPreviewInfoItem
          icon="briefcase-time"
          title={t('contract_duration')}
          subTitle={t(job.contractDuration.toLowerCase())}
        />
      )}
      {!!job.hoursPerWeek && (
        <JobPreviewInfoItem
          icon="user-time"
          title={t('shift_hours')}
          subTitle={translateReplacer(t('num_hours_per_week'), [
            (job.hoursPerWeek / 60).toString(),
          ])}
        />
      )}
      <JobPreviewInfoItem
        icon="location-v2"
        title={t('location')}
        subTitle={job.location?.title ?? ''}
      />
    </SectionLayout>
  );
};

export default JobPreview;
