import type { FC } from 'react';
import SectionLayout from 'shared/components/Organism/Objects/Common/Section.layout';
import type { LanguageLevelType, LanguageProps } from 'shared/types/jobsProps';
import cnj from 'shared/uikit/utils/cnj';
import useTranslation from 'shared/utils/hooks/useTranslation';
import ProgressItem from 'shared/uikit/ProgressItem';
import { type JobFullDetailsClassNameProps } from '@shared/components/molecules/Job/types';
import classes from './JobStyles.module.scss';

interface JobLanguagesProps extends JobFullDetailsClassNameProps {
  languages: LanguageProps[];
}

const JobLanguages: FC<JobLanguagesProps> = ({ languages, classNames }) => {
  const { t } = useTranslation();

  return (
    <SectionLayout
      title={t('languages')}
      classNames={{
        childrenWrap: cnj(classes.root, classNames?.sectionLayout),
      }}
    >
      {languages.map((language) => (
        <ProgressItem
          key={`language_${language.languageId}`}
          title={language.languageName}
          progressValue={languageLevelProgress[language.languageLevel]}
          progressSteps={7}
          tooltipText={t(language.languageLevel)}
        />
      ))}
    </SectionLayout>
  );
};

export default JobLanguages;

const languageLevelProgress: { [key in LanguageLevelType]: number } = {
  A1: 1,
  A2: 2,
  B1: 3,
  B2: 4,
  C1: 5,
  C2: 6,
  Native: 7,
};
