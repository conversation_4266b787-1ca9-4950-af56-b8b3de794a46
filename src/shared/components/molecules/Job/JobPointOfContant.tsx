import SearchCard from 'shared/components/Organism/SearchCard';
import type { JobCollaboratorProps } from 'shared/types/jobsProps';
import { useMemo, type FC } from 'react';
import SectionLayout from 'shared/components/Organism/Objects/Common/Section.layout';
import cnj from 'shared/uikit/utils/cnj';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useAccessibility from '@shared/hooks/useAccessibility';
import { FallbackText } from '@shared/components/atoms/FallbackText';
import type { JobFullDetailsClassNameProps } from '@shared/components/molecules/Job/types';
import classes from './JobStyles.module.scss';

interface JobPointOfContantProps extends JobFullDetailsClassNameProps {
  collaborators: JobCollaboratorProps[];
}

const JobPointOfContant: FC<JobPointOfContantProps> = ({
  collaborators,
  classNames,
}) => {
  const { t } = useTranslation();
  const { accessName } = useAccessibility();

  const pointOfContact = useMemo(
    () => collaborators.find((collab) => !!collab.pointOfContact),
    [collaborators]
  );

  return (
    <SectionLayout
      title={t('point_of_contact')}
      classNames={{
        childrenWrap: cnj(
          classes.root,
          classes.noPadding,
          classNames?.sectionLayout
        ),
      }}
    >
      <SearchCard
        imgSrc={pointOfContact?.croppedImageUrl ?? ''}
        firstText={`${pointOfContact?.name} ${pointOfContact?.surname}`}
        secondText={accessName(pointOfContact as any)}
        fourthText={<FallbackText value={pointOfContact?.location} />}
        thirdText={pointOfContact?.occupation ?? ''}
        isHoverAble={false}
        className={classes.poc}
      />
    </SectionLayout>
  );
};

export default JobPointOfContant;
