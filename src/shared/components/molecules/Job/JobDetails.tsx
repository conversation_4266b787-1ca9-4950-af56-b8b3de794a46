import Flex from 'shared/uikit/Flex';
import type { FC } from 'react';
import InfoCard from 'shared/components/Organism/Objects/Common/InfoCard';
import SectionLayout from 'shared/components/Organism/Objects/Common/Section.layout';
import cnj from 'shared/uikit/utils/cnj';
import useTranslation from 'shared/utils/hooks/useTranslation';
import RichTextEditor from '@shared/uikit/RichTextEditor';
import type { JobFullDetailsClassNameProps } from '@shared/components/molecules/Job/types';
import classes from './JobStyles.module.scss';

interface JobDetailsProps extends JobFullDetailsClassNameProps {
  hashtags?: string[];
  description?: string;
}

const JobDetails: FC<JobDetailsProps> = ({
  description,
  hashtags,
  classNames,
}) => {
  const { t } = useTranslation();
  return (
    <SectionLayout
      title={t('details')}
      classNames={{
        childrenWrap: cnj(classes.root, classNames?.sectionLayout),
      }}
    >
      {!!description && (
        <RichTextEditor
          value={description}
          readOnly
          styles="!p-0 !border-0"
          jobCreation
        />
      )}
      {!!hashtags?.length && (
        <Flex>
          <InfoCard
            disabledHover
            icon="hashtag1"
            wrapperClassName={classes.infoCard}
            valueProps={{ color: 'primaryText' }}
            title="Hashtags"
            subTitle={hashtags.map((hash: string) => `#${hash}`).join(', ')}
          />
        </Flex>
      )}
    </SectionLayout>
  );
};

export default JobDetails;
