import type { JobBenefitProps } from 'shared/types/jobsProps';
import Flex from 'shared/uikit/Flex';
import Icon from 'shared/uikit/Icon';
import Typography from 'shared/uikit/Typography';
import { type FC, type PropsWithChildren } from 'react';
import SectionLayout from 'shared/components/Organism/Objects/Common/Section.layout';
import cnj from 'shared/uikit/utils/cnj';
import useTranslation from 'shared/utils/hooks/useTranslation';
import type { JobFullDetailsClassNameProps } from '@shared/components/molecules/Job/types';
import classes from './JobStyles.module.scss';

interface JobBenefitsProps extends JobFullDetailsClassNameProps {
  benefits: JobBenefitProps[];
}

const JobBenefits: FC<JobBenefitsProps> = ({ benefits, classNames }) => {
  const { t } = useTranslation();

  return (
    <SectionLayout
      title={t('benefits')}
      classNames={{
        childrenWrap: cnj(classes.root, classNames?.sectionLayout),
      }}
    >
      <Flex className={classes.benefitsRoot}>
        {benefits?.map((benefit) => (
          <BenefitItem key={`benefit_${benefit.id}`}>
            {benefit.title}
          </BenefitItem>
        ))}
      </Flex>
    </SectionLayout>
  );
};

export default JobBenefits;

const BenefitItem: FC<PropsWithChildren> = ({ children }) => (
  <Flex className={classes.benefitItem}>
    <Icon name="check-circle" color="success" size={24} />
    <Typography>{children}</Typography>
  </Flex>
);
