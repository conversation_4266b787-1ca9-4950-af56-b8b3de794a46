import type { ButtonProps } from '@shared/uikit/Button';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import SkillPickerActions from '@shared/uikit/SkillPicker/SkillPickerActions';
import Typography from '@shared/uikit/Typography';

const levels = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

export interface Skillboard {
  title?: string;
  level?: number;
  id: string;
}

interface SkillboardItemProps {
  item: Skillboard;
  onEdit: (skill: Skillboard) => void;
  onDelete: (skillId: string) => void;
  onSelect: (skill: { level: number; id: string }) => void;
  activeScore: number;
}

const getSchema = (lvl: number, value = 0, activeScore = 0) => {
  const color = lvl < 6 ? 'error' : lvl > 8 ? 'success' : 'orange';
  return (color +
    (lvl !== value && lvl != activeScore
      ? '-semi-transparent'
      : '')) as ButtonProps['schema'];
};

export default function SkillboardItem({
  item,
  onEdit,
  onDelete,
  onSelect,
  activeScore,
}: SkillboardItemProps) {
  return (
    <Flex className="gap-8 relative">
      <SkillPickerActions
        onEdit={() => onEdit(item)}
        onDelete={() => onDelete(item.id)}
      />
      <Typography size={15} font="700" height={21}>
        {item.title}
      </Typography>
      <Flex flexDir="row" className="justify-between">
        {levels.map((lvl) => (
          <Button
            key={lvl}
            label={lvl}
            schema={getSchema(lvl, item.level, activeScore)}
            onClick={() => onSelect({ level: lvl, id: item.id })}
          />
        ))}
      </Flex>
    </Flex>
  );
}
