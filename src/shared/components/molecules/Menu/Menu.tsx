import cnj from '@shared/uikit/utils/cnj';
import Tooltip from '@shared/uikit/Tooltip';
import type { MenuProps } from 'shared/types/components/Menu.type';
import IconButton from 'shared/uikit/Button/IconButton';
import PopperItem from 'shared/uikit/PopperItem';
import PopperMenu from 'shared/uikit/PopperMenu';
import classes from './Menu.module.scss';

const Menu = ({
  menuItems,
  menuItemSize,
  menuIcon = 'ellipsis-h',
  menuIconSize,
  menuIconColorSchema,
  className,
  menuPlacement,
  popperMenuProps,
  classNames,
  ref,
}: MenuProps) => (
  <PopperMenu
    ref={ref}
    buttonComponent={
      <IconButton
        name={menuIcon}
        colorSchema={menuIconColorSchema}
        className={className}
        size={menuIconSize}
      />
    }
    popperContainerClassName={cnj(classes.menu, classNames?.menu)}
    placement={menuPlacement}
    {...popperMenuProps}
  >
    {menuItems.map((mi) =>
      mi?.tooltipTitle ? (
        <Tooltip
          key={`tooltip-m-${mi.label}-i`}
          placement="top"
          trigger={
            <PopperItem
              disabled={mi.disabled}
              label={mi.label}
              iconName={mi.iconName}
              iconSize={mi.iconSize || menuItemSize || 24}
              className={cnj(classes.item, mi.className)}
              onClick={mi.onClick}
              iconClassName={{
                wrapper: classNames?.itemIconWrapper,
                icon: classNames?.itemIcon,
              }}
            />
          }
        >
          {mi?.tooltipTitle}
        </Tooltip>
      ) : (
        <PopperItem
          key={`m-${mi.label}-i`}
          disabled={mi.disabled}
          label={mi.label}
          iconName={mi.iconName}
          iconSize={mi.iconSize || menuItemSize || 24}
          className={cnj(classes.item, mi.className)}
          onClick={mi.onClick}
          iconClassName={{
            wrapper: classNames?.itemIconWrapper,
            icon: classNames?.itemIcon,
          }}
        />
      )
    )}
  </PopperMenu>
);

export default Menu;
