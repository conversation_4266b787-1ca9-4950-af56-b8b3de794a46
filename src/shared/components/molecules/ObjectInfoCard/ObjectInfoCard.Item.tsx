import React from 'react';
import Flex from 'shared/uikit/Flex';
import OverflowTip from 'shared/uikit/Typography/OverflowTip';
import Icon from 'shared/uikit/Icon';
import cnj from 'classnames';
import classes from './ObjectInfoCard.component.module.scss';

interface Props {
  icon: string;
  title: string | React.ReactNode;
  className?: string;
  iconSize?: number;
  iconclassName?: string;
}

const ObjectInfoCardItem = ({
  icon,
  title,
  className,
  iconSize = 16,
  iconclassName,
}: Props): JSX.Element => (
  <Flex className={cnj(classes.rowItems, className)}>
    <Icon
      size={iconSize}
      type="far"
      name={icon}
      color="smoke_coal"
      className={iconclassName}
    />
    <OverflowTip color="smoke_coal" ml={6} size={14} height={16} isTruncated>
      {title}
    </OverflowTip>
  </Flex>
);

export default ObjectInfoCardItem;
