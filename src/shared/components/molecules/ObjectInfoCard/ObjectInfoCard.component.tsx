import type { JSX, ComponentType, PropsWithChildren } from 'react';
import React from 'react';
import Flex from 'shared/uikit/Flex';
import OverflowTip from 'shared/uikit/Typography/OverflowTip';
import FollowingYouIcon from 'shared/components/molecules/FollowingYouIcon';
import cnj from 'shared/uikit/utils/cnj';
import type { AvatarProps } from 'shared/uikit/Avatar';
import Avatar from 'shared/uikit/Avatar';
import useTranslation from 'shared/utils/hooks/useTranslation';
import type { IconName } from '@shared/uikit/Icon';
import { FallbackText } from '@shared/components/atoms/FallbackText';
import classes from './ObjectInfoCard.component.module.scss';
import UserInfoCardDetailItem from './ObjectInfoCard.Item';

interface Props {
  firstText?: string;
  secondText?: string;
  thirdText?: string;
  thirdTextIcon?: IconName;
  fourthText?: string;
  fourthTextIcon?: IconName;
  isFollowingYou?: boolean;
  isPage?: boolean;
  isFirstTextSmall?: boolean;
  isSecondTextSmall?: boolean;
  actions?: ReactNode;
  FirstTextWrapper?: ComponentType<PropsWithChildren>;
  SecondTextWrapper?: ComponentType<PropsWithChildren>;
  avatar?: string;
  withAvatar?: boolean;
  avatarProps?: AvatarProps;
  visibleThirdText?: boolean;
  visibleFourthText?: boolean;
  className?: string;
  classNames?: {
    firstLineWrapper?: string;
  };
}

const ObjectInfoCard = ({
  firstText,
  thirdText,
  thirdTextIcon,
  secondText,
  fourthText,
  fourthTextIcon,
  isFollowingYou,
  isPage,
  isFirstTextSmall,
  isSecondTextSmall,
  FirstTextWrapper = React.Fragment,
  SecondTextWrapper = React.Fragment,
  withAvatar = true,
  actions,
  avatar,
  avatarProps,
  visibleFourthText = true,
  visibleThirdText = true,
  className,
  classNames,
}: Props): JSX.Element => {
  const { t } = useTranslation();

  return (
    <Flex flexDir="row" className={className}>
      {withAvatar && (
        <Avatar
          className={classes.marginRight}
          size="flg"
          isCompany={isPage}
          imgSrc={avatar}
          {...avatarProps}
        />
      )}
      <Flex className={classes.userInfoContainer}>
        <Flex>
          <Flex
            flexDir="row"
            className={cnj(
              classes.actionMenuWrapper,
              classNames?.firstLineWrapper
            )}
          >
            <FirstTextWrapper>
              <OverflowTip
                color="smoke_coal"
                size={isFirstTextSmall ? 20 : 24}
                height={isFirstTextSmall ? 23 : 28}
                font="700"
                isTruncated
              >
                {firstText}
              </OverflowTip>
            </FirstTextWrapper>
          </Flex>
          {actions && <div className="absolute top-0 right-0">{actions}</div>}
          <Flex
            className={cnj(
              classes.userNameWrapper,
              isFirstTextSmall && classes.zeroMarginBottom
            )}
          >
            <SecondTextWrapper>
              <OverflowTip
                color="secondaryDisabledText"
                size={isSecondTextSmall ? 12 : 14}
                height={isSecondTextSmall ? 14 : 16}
                isTruncated
                mr={8}
              >
                {secondText}
              </OverflowTip>
            </SecondTextWrapper>
            {isFollowingYou && <FollowingYouIcon />}
          </Flex>
        </Flex>
        {thirdText && visibleThirdText && (
          <UserInfoCardDetailItem
            title={thirdText}
            icon={thirdTextIcon ?? (isPage ? 'category' : 'job')}
            className={cnj(classes.marginTop4)}
            iconclassName="mr-[1px]"
            iconSize={14}
          />
        )}
        {visibleFourthText && (
          <UserInfoCardDetailItem
            icon={fourthTextIcon ?? 'location'}
            title={<FallbackText value={fourthText} />}
            className={cnj(classes.marginTop4, classes.hack)}
          />
        )}
      </Flex>
    </Flex>
  );
};

export default ObjectInfoCard;
