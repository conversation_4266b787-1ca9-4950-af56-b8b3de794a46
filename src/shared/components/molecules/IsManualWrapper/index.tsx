import Flex from '@shared/uikit/Flex';
import Typography from '@shared/uikit/Typography';
import useTranslation from '@shared/utils/hooks/useTranslation';
import React, { type PropsWithChildren } from 'react';

export function IsManualWrapper({ children }: PropsWithChildren) {
  const { t } = useTranslation();
  return (
    <Flex
      flexDir="row"
      alignItems="center"
      className="flex-1 justify-between gap-10"
    >
      {children}
      <Typography color="success" size={14} height={16} font="500" isTruncated>
        {t('manually_created')}
      </Typography>
    </Flex>
  );
}
