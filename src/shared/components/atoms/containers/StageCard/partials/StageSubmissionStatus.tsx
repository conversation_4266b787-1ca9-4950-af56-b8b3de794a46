import Flex from '@shared/uikit/Flex';
import Icon from '@shared/uikit/Icon';
import Typography from '@shared/uikit/Typography';
import AvatarCard from '@shared/uikit/AvatarCard';
import Avatar from '@shared/uikit/Avatar';
import useTranslation from '@shared/utils/hooks/useTranslation';
import useGetAppObject from '@shared/hooks/useGetAppObject';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';

import type { StageCardProps } from '../index';

interface SubmissionStatusProps {
  collaboratorSubmitted: StageCardProps.CollaboratorSubmitted;
  vendorSubmitted: StageCardProps.VendorSubmitted;
  repeated: boolean;
  id: string;
}

export function SubmissionStatus({
  collaboratorSubmitted,
  repeated,
  id,
  vendorSubmitted,
}: SubmissionStatusProps) {
  const { t } = useTranslation();
  const { authUser } = useGetAppObject();
  const appDispatch = useGlobalDispatch();

  const onOpenDuplicationVendors = () => {
    appDispatch({
      type: 'OPEN_DUPLICATION_VENDORS_PANEL',
      payload: { id, vendorSubmitted },
    });
  };

  console.log('repeated', repeated);

  // if (repeated) {
  if (true) {
    return (
      <Flex
        flexDir="row"
        className="gap-4 items-center cursor-pointer"
        onClick={(e) => {
          e.stopPropagation();
          onOpenDuplicationVendors();
        }}
      >
        <Icon name="info-circle" type="fal" size={16} color="error" />
        <Typography fontSize={14} fontWeight={500} color="error">
          {t('duplicated')}
        </Typography>
      </Flex>
    );
  }

  if (collaboratorSubmitted?.id) {
    if (
      authUser?.id &&
      collaboratorSubmitted?.id?.toString() === authUser?.id
    ) {
      return (
        <Flex flexDir="row" className="gap-8 items-center">
          <Typography
            fontSize={14}
            fontWeight={500}
            color="secondaryDisabledText"
          >
            {t('submitted_by')}:
          </Typography>
          <Typography fontSize={14} fontWeight={500} color="smoke_coal">
            {t('you_cap')}
          </Typography>
        </Flex>
      );
    }
    return (
      <Flex flexDir="row" className="gap-8 items-center">
        <Typography
          fontSize={14}
          fontWeight={500}
          color="secondaryDisabledText"
        >
          {t('submitted_by')}:
        </Typography>
        <Flex flexDir="row" className="items-center">
          <AvatarCard
            withPadding={false}
            avatarProps={{ isCompany: true, size: 'mini' }}
            data={{
              image: collaboratorSubmitted?.croppedImageUrl,
            }}
          />
          <Typography fontSize={14} fontWeight={500} color="smoke_coal">
            {collaboratorSubmitted?.name}
          </Typography>
        </Flex>
      </Flex>
    );
  }

  if (vendorSubmitted && !!vendorSubmitted.length) {
    return (
      <Flex flexDir="row" className="gap-8 items-center">
        <Typography
          fontSize={14}
          fontWeight={500}
          color="secondaryDisabledText"
        >
          {t('submitted_by')}:
        </Typography>
        <Flex flexDir="row" className="gap-4 items-center">
          <Avatar
            size="mini"
            imgSrc={vendorSubmitted[0]?.pageInfo?.croppedImageUrl}
          />
          <Typography fontSize={14} fontWeight={500} color="smoke_coal">
            {vendorSubmitted[0]?.pageInfo?.title}
          </Typography>
        </Flex>
      </Flex>
    );
  }

  return null;
}
