import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import { Endpoints } from 'shared/utils/constants';
import useTranslation from 'shared/utils/hooks/useTranslation';
import formValidator from 'shared/utils/form/formValidator';
import useMedia from 'shared/uikit/utils/useMedia';
import Flex from 'shared/uikit/Flex';
import InfoCard from 'shared/uikit/InfoCard';
import cnj from 'shared/uikit/utils/cnj';
import BaseButton from 'shared/uikit/Button/BaseButton';
import Typography from 'shared/uikit/Typography';
import Icon from 'shared/uikit/Icon';
import PopperMenu from 'shared/uikit/PopperMenu';
import PopperItem from 'shared/uikit/PopperItem';
import { useState } from 'react';
import { useMultiStepFormState } from 'shared/hooks/useMultiStepForm';
import type { FormikErrors } from 'formik';
import Observer from '@shared/components/atoms/Observer';
import classes from './useUploadResume.module.scss';
import type { CallbackParams, MultiStepFormProps } from '../MultiStepForm';
import { useDiscardConfirm } from '../ProfileSections/sections/utils/useDiscardConfirm';
import { profileSectionsStepKeys } from '../ProfileSections/constants';
import {
  getBioGroups,
  getBioValidationSchema,
  transformBio,
} from '../ProfileSections/sections/useBio';
import {
  getExperienceGroups,
  getExperienceValidationSchema,
  transformExperience,
} from '../ProfileSections/sections/useExperience';
import FormItem from './FormItem';
import { TwoButtonFooter } from '../ProfileSections/Components/TwoButtonFooter';
import {
  getSchoolGroups,
  getSchoolValidationSchema,
  transformSchool,
} from '../ProfileSections/sections/useSchool';
import {
  getCourseGroups,
  getCourseValidationSchema,
  transformCourse,
} from '../ProfileSections/sections/useCourse';
import {
  getLicenseGroups,
  getLicenseValidationSchema,
  transformLicence,
} from '../ProfileSections/sections/useLicence';
import {
  getPublicationGroups,
  getPublicationValidationSchema,
  transformPublication,
} from '../ProfileSections/sections/usePublication';
import {
  getHonorGroups,
  getHonorValidationSchema,
  transformHonor,
} from '../ProfileSections/sections/useHonor';
import {
  getPatentGroups,
  getPatentvalidationSchema,
  transformPatent,
} from '../ProfileSections/sections/usePatent';
import { setFormTempInitialData } from './setFormTempInitialData';
import useAuthUser from '@shared/utils/hooks/useAuthUser';

type SingleDataItem = {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  getStepHeaderProps: Exclude<
    MultiStepFormProps['getStepHeaderProps'],
    undefined
  >;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
  renderFooter: Exclude<MultiStepFormProps['renderFooter'], undefined>;
  getValidationSchema?: Exclude<
    MultiStepFormProps['getValidationSchema'],
    undefined
  >;
  onSuccess?: Exclude<MultiStepFormProps['onSuccess'], undefined>;
  formName?: string;
  url?: string;
  transform?: Exclude<MultiStepFormProps['transform'], undefined>;
};

export function useUploadResume(): SingleDataItem[] {
  const { t } = useTranslation();
  const { isMoreThanTablet } = useMedia();
  const { openConfirm } = useDiscardConfirm();
  const { tempInitialData } = useMultiStepFormState('resumeUpload');
  const [forceVisibleError, setForceVisibleError] = useState(false);
  const { data: authUser } = useAuthUser();
  const countryCode = authUser?.location?.countryCode;

  const scrollErrorIntoView = ({
    step,
    errors,
  }: {
    step: number;
    errors: FormikErrors<any>;
  }) => {
    const formName = data[step]?.formName || '';
    if (formName in errors) {
      const formIndex = (errors[formName] as any)?.findIndex(Boolean);
      const el = document.querySelector(
        `[data-name="${formName}-${formIndex}"]`
      );
      el?.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const renderFooter: SingleDataItem['renderFooter'] = ({
    setStep,
    step,
    isSubmitStep,
    values,
    resetForm,
    submitForm,
    validateForm,
    isSubmitting,
  }) => {
    const handleSubmit = async () => {
      const errors = await validateForm?.();
      if (!Object?.keys(errors || {})?.length) {
        setForceVisibleError(false);
        submitForm?.();
      } else {
        setForceVisibleError(true);
        scrollErrorIntoView({ step, errors });
      }
    };

    return (
      <>
        <Observer
          step={step}
          validateForm={() => {
            setFormTempInitialData(values);
            resetForm({ values });
            setForceVisibleError(false);
          }}
        />
        <TwoButtonFooter
          secondaryButtonLabel={t('discard')}
          submitLabel={isSubmitStep ? t('update') : t('next')}
          onSubmitClick={handleSubmit}
          disabledSubmit={isSubmitting}
        />
      </>
    );
  };

  const getHeaderProps: SingleDataItem['getHeaderProps'] = ({
    setStep,
    step,
    setFieldValue,
    dirty,
  }) => ({
    title: t('update_profile'),
    hideBack: step === 0,
    backButtonProps: {
      onClick: () => {
        const func = (isReset?: boolean) => {
          if (isReset) {
            const key = data?.[step]?.formName;
            setFieldValue?.(key || '', tempInitialData[key as string]);
          }
          setStep((prev) => prev - 1);
        };
        if (dirty) {
          openConfirm(() => func(true));
        } else {
          func();
        }
      },
    },
    noCloseButton: step !== 0,
  });

  const getStepHeaderProps = ({ title, iconProps }: any) =>
    (({ setFieldValue, step } = {} as any) => ({
      title,
      iconProps: {
        type: 'far',
        ...iconProps,
      },
      renderTitle: ({ setStep, dirty }) => (
        <PopperMenu
          placement="bottom-start"
          closeOnScroll
          buttonComponent={
            <BaseButton className={classes.row}>
              <Typography size={16} height={18} font="700" color="primaryText">
                {title}
              </Typography>
              <Icon
                size={13}
                name="chevron-down"
                className={classes.chevronDown}
              />
            </BaseButton>
          }
        >
          <PopperList
            list={data}
            setStep={setStep}
            dirty={dirty}
            setFieldValue={setFieldValue}
            step={step}
          />
        </PopperMenu>
      ),
    })) as SingleDataItem['getStepHeaderProps'];

  const getArrayValidationSchema: (
    groupName: string,
    getValidationFunc: Function
  ) => MultiStepFormProps['getValidationSchema'] =
    (groupName, getValidationFunc) => () => () =>
      formValidator.object().shape({
        [groupName]: formValidator
          .array()
          .notRequired()
          .of(getValidationFunc?.() as any),
      }) as any;

  const data: Array<SingleDataItem> = [
    {
      stepKey: profileSectionsStepKeys.BIO,
      getHeaderProps,
      renderFooter,
      getStepHeaderProps: getStepHeaderProps({
        title: t('biography'),
        iconProps: {
          name: 'address-card',
        },
      }),
      renderBody: ({ values }) => (
        <Flex className={classes.wrapper}>
          <WarningCard />
          <DynamicFormBuilder
            className={classes.formRoot}
            groups={getBioGroups({ t })}
          />
        </Flex>
      ),
      getValidationSchema: getBioValidationSchema,
      formName: 'bio',
      url: Endpoints.App.User.Update.biography,
      transform: transformBio,
    },
    {
      stepKey: profileSectionsStepKeys.PRO_EXPERIENCE,
      getValidationSchema: getArrayValidationSchema(
        'experiences',
        getExperienceValidationSchema
      ),
      getHeaderProps,
      renderFooter,
      getStepHeaderProps: getStepHeaderProps({
        title: t('experience'),
        iconProps: {
          name: 'briefcase-blank',
        },
      }),
      renderBody: ({ values }) => (
        <Flex className={classes.wrapper}>
          <WarningCard />
          <FormItem
            forceVisibleError={forceVisibleError}
            groupName="experiences"
            singleGroups={getExperienceGroups({
              t,
              isMoreThanTablet,
              isVolunteer: false,
              values,
              countryCode,
            })}
            values={values}
            classNames={{ formRoot: cnj(classes.formRoot) }}
            title={t('experience')}
          />
        </Flex>
      ),
      formName: 'experiences',
      url: Endpoints.App.User.Experiences.get,
      transform: ({ experiences }: any) => ({
        items: experiences?.map(transformExperience),
      }),
    },
    {
      stepKey: profileSectionsStepKeys.VOL_EXPERIENCE,
      getHeaderProps,
      renderFooter,
      getStepHeaderProps: getStepHeaderProps({
        title: t('volunteer'),
        iconProps: {
          name: 'volunteer',
        },
      }),
      renderBody: ({ values }) => (
        <Flex className={classes.wrapper}>
          <WarningCard />
          <FormItem
            forceVisibleError={forceVisibleError}
            groupName="volunteers"
            singleGroups={getExperienceGroups({
              t,
              isMoreThanTablet,
              isVolunteer: true,
              values,
              countryCode,
            })}
            values={values}
            classNames={{ formRoot: cnj(classes.formRoot) }}
            title={t('volunteer')}
          />
        </Flex>
      ),
      getValidationSchema: getArrayValidationSchema(
        'volunteers',
        getExperienceValidationSchema
      ),
      formName: 'volunteers',
      url: Endpoints.App.User.Volunteers.get,
      transform: ({ volunteers }: any) => ({
        items: volunteers?.map(transformExperience),
      }),
    },
    {
      stepKey: profileSectionsStepKeys.SCHOOL,
      getHeaderProps,
      renderFooter,
      getStepHeaderProps: getStepHeaderProps({
        title: t('school'),
        iconProps: {
          name: 'school',
        },
      }),
      renderBody: ({ values }) => (
        <Flex className={classes.wrapper}>
          <WarningCard />
          <FormItem
            forceVisibleError={forceVisibleError}
            groupName="educations"
            singleGroups={getSchoolGroups({
              t,
              isMoreThanTablet,
              values,
              countryCode,
            })}
            values={values}
            classNames={{ formRoot: cnj(classes.formRoot) }}
            title={t('school')}
          />
        </Flex>
      ),
      getValidationSchema: getSchoolValidationSchema,
      formName: 'educations',
      url: Endpoints.App.User.Education.get,
      transform: ({ educations }: any) => ({
        items: educations?.map(transformSchool),
      }),
    },
    {
      stepKey: profileSectionsStepKeys.COURSE,
      getHeaderProps,
      renderFooter,
      getStepHeaderProps: getStepHeaderProps({
        title: t('course'),
        iconProps: {
          name: 'course',
        },
      }),
      renderBody: ({ values }) => (
        <Flex className={classes.wrapper}>
          <WarningCard />
          <FormItem
            forceVisibleError={forceVisibleError}
            groupName="courses"
            singleGroups={getCourseGroups({
              t,
              isMoreThanTablet,
              values,
              countryCode,
            })}
            values={values}
            classNames={{ formRoot: cnj(classes.formRoot) }}
            title={t('course')}
          />
        </Flex>
      ),
      getValidationSchema: getArrayValidationSchema(
        'courses',
        getCourseValidationSchema
      ),
      formName: 'courses',
      url: Endpoints.App.User.Course.get,
      transform: ({ courses }: any) => ({
        items: courses?.map(transformCourse),
      }),
    },
    {
      stepKey: profileSectionsStepKeys.LICENCE,
      getHeaderProps,
      renderFooter,
      getStepHeaderProps: getStepHeaderProps({
        title: t('licence_certificate'),
        iconProps: {
          name: 'license',
        },
      }),
      renderBody: ({ values }) => (
        <Flex className={classes.wrapper}>
          <WarningCard />
          <FormItem
            forceVisibleError={forceVisibleError}
            groupName="certifications"
            singleGroups={getLicenseGroups({
              t,
              isMoreThanTablet,
              values,
              countryCode,
            })}
            values={values}
            classNames={{ formRoot: cnj(classes.formRoot) }}
            title={t('license')}
          />
        </Flex>
      ),
      getValidationSchema: getArrayValidationSchema(
        'certifications',
        getLicenseValidationSchema
      ),
      formName: 'certifications',
      url: Endpoints.App.User.Licence.get,
      transform: ({ certifications }: any) => ({
        items: certifications?.map(transformLicence),
      }),
    },
    {
      stepKey: profileSectionsStepKeys.SKILL,
      getHeaderProps,
      renderFooter,
      getStepHeaderProps: getStepHeaderProps({
        title: t('skill'),
        iconProps: {
          name: 'skill',
        },
      }),
      renderBody: ({ values }) => (
        <Flex className={classes.wrapper}>
          <WarningCard />
          <DynamicFormBuilder
            groups={[
              {
                name: 'skills',
                cp: 'skillPicker',
                classNames: { listWrapper: classes.noPaddingTop },
              },
            ]}
          />
        </Flex>
      ),
      formName: 'skills',
      url: Endpoints.App.User.Skill.get,
      transform: ({ skills }: any) => ({
        items: skills?.map((s: any) => ({
          level: s.skillLevel,
          name: s.label,
        })),
      }),
    },
    {
      stepKey: profileSectionsStepKeys.LANGUAGE,
      getHeaderProps,
      renderFooter,
      getStepHeaderProps: getStepHeaderProps({
        title: t('language'),
        iconProps: {
          name: 'language',
        },
      }),
      renderBody: ({ values }) => (
        <Flex className={classes.wrapper}>
          <WarningCard />
          <DynamicFormBuilder
            groups={[
              {
                name: 'languages',
                cp: 'languagePicker',
                classNames: { listWrapper: classes.noPaddingTop },
              },
            ]}
          />
        </Flex>
      ),
      formName: 'languages',
      url: Endpoints.App.User.Language.get,
      transform: ({ languages }: any) => ({
        items: languages?.map((l: any) => ({
          level: l.languageLevel,
          name: l.label,
        })),
      }),
    },
    {
      stepKey: profileSectionsStepKeys.PUBLICATION,
      getHeaderProps,
      renderFooter,
      getStepHeaderProps: getStepHeaderProps({
        title: t('publication'),
        iconProps: {
          name: 'publication',
        },
      }),
      renderBody: ({ values }) => (
        <Flex className={classes.wrapper}>
          <WarningCard />
          <FormItem
            forceVisibleError={forceVisibleError}
            groupName="publications"
            singleGroups={getPublicationGroups({
              t,
              isMoreThanTablet,
              values,
              countryCode,
            })}
            values={values}
            classNames={{ formRoot: cnj(classes.formRoot) }}
            title={t('publication')}
          />
        </Flex>
      ),
      getValidationSchema: getArrayValidationSchema(
        'publications',
        getPublicationValidationSchema
      ),
      formName: 'publications',
      url: Endpoints.App.User.Publication.get,
      transform: ({ publications }: any) => ({
        items: publications?.map(transformPublication),
      }),
    },
    {
      stepKey: profileSectionsStepKeys.HONOR,
      getHeaderProps,
      renderFooter,
      getStepHeaderProps: getStepHeaderProps({
        title: t('honor_aw'),
        iconProps: {
          name: 'honor',
        },
      }),
      renderBody: ({ values }) => (
        <Flex className={classes.wrapper}>
          <WarningCard />
          <FormItem
            forceVisibleError={forceVisibleError}
            groupName="awards"
            singleGroups={getHonorGroups({
              t,
              isMoreThanTablet,
              values,
              countryCode,
            })}
            values={values}
            classNames={{ formRoot: cnj(classes.formRoot) }}
            title={t('honor_aw')}
          />
        </Flex>
      ),
      getValidationSchema: getArrayValidationSchema(
        'awards',
        getHonorValidationSchema
      ),
      formName: 'awards',
      url: Endpoints.App.User.Honor.get,
      transform: ({ awards }: any) => ({ items: awards?.map(transformHonor) }),
    },
    {
      stepKey: profileSectionsStepKeys.PATENT,
      getHeaderProps,
      renderFooter,
      getStepHeaderProps: getStepHeaderProps({
        title: t('patent'),
        iconProps: {
          name: 'patent',
        },
      }),
      renderBody: ({ values }) => (
        <Flex className={classes.wrapper}>
          <WarningCard />
          <FormItem
            forceVisibleError={forceVisibleError}
            groupName="patents"
            singleGroups={getPatentGroups({
              t,
              isMoreThanTablet,
              values,
              countryCode,
            })}
            values={values}
            classNames={{ formRoot: cnj(classes.formRoot) }}
            title={t('patent')}
          />
        </Flex>
      ),
      getValidationSchema: getArrayValidationSchema(
        'patent',
        getPatentvalidationSchema
      ),
      formName: 'patents',
      url: Endpoints.App.User.Patent.get,
      transform: ({ patents }: any) => ({
        items: patents?.map(transformPatent),
      }),
    },
  ];

  return data;
}

const WarningCard = () => {
  const { t } = useTranslation();
  return (
    <InfoCard>
      {t('clicking_next_will_overwrite_your_current_profile')}
    </InfoCard>
  );
};

const PopperList = ({
  list,
  setStep,
  dirty,
  setFieldValue,
  step,
}: {
  list: Array<SingleDataItem>;
  setStep: CallbackParams['setStep'];
  dirty?: boolean;
  setFieldValue?: any;
  step: number;
}) => {
  const { openConfirm } = useDiscardConfirm();
  const { tempInitialData } = useMultiStepFormState('resumeUpload');

  const handleClick = (item: SingleDataItem) => () => {
    const index = list?.findIndex((_item) => _item?.stepKey === item?.stepKey);
    if (index > -1) {
      const func = (isReset?: boolean) => {
        if (isReset) {
          const key = list?.[step]?.formName;
          setFieldValue(key, tempInitialData[key as string]);
        }
        setStep(index);
      };
      if (dirty) {
        openConfirm(() => func(true));
      } else {
        func();
      }
    }
  };

  return (
    <>
      {list.map((item: any) => {
        const {
          title: label,
          iconProps: { name: icon },
        } = item?.getStepHeaderProps?.();

        return (
          <PopperItem
            onClick={handleClick(item)}
            iconName={icon}
            label={label || ''}
            key={label}
            labelTruncated
            secondLabelTruncated
            iconSize={20}
            labelClassName={classes.popperItemLabel}
          />
        );
      })}
    </>
  );
};
