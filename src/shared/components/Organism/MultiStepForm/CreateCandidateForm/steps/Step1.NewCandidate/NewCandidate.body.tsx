import ModalTransitionWrapper from '@shared/uikit/Modal/TransitionWrapper/ModalTransitionWrapper';
import {
  candidateEndpoints,
  storageEndPoints,
} from '@shared/utils/constants/servicesEndpoints';
import { useMemo } from 'react';
import ResumePlusIcon from 'shared/svg/ResumePlusIcon';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import geoApi from 'shared/utils/api/geo';
import { Endpoints } from 'shared/utils/constants';
import useTranslation from 'shared/utils/hooks/useTranslation';
import lookupResponseNormalizer from 'shared/utils/normalizers/lookupResponseNormalizer';
import type { CreateCandidateFormData } from '@shared/types/candidates';
import Flex from '@shared/uikit/Flex';
import { ShareEntities, ShareEntityTab } from '@shared/types/share/entities';
import { UploadedResumeCard } from '@shared/components/Organism/UploadedResumeSection/UploadedResumeCard.component';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';
import type { FormikHelpers } from 'formik';
import { useFormikContext } from 'formik';
import type { CandidateUploadFileResponse } from '@shared/types/api/storage';
import classes from '../../CreateCandidate.module.scss';
import type { CallbackParams } from '../../../MultiStepForm';

const NewCandidateBody: React.FC<CallbackParams> = () => {
  const { t } = useTranslation();
  const { values, setFieldValue } = useFormikContext<CreateCandidateFormData>();

  const formGroups = useMemo(
    () => [
      {
        formGroup: {
          title: t('general_info'),
        },
        name: 'email',
        cp: 'input',
        label: t('email'),
        wrapStyle: classes.formItemWrapStyle,
        required: true,
      },
      {
        name: 'name',
        label: t('firstName'),
        required: true,
        isFirstHalfWidth: true,
        cp: 'input',
      },
      {
        name: 'surname',
        label: t('lastName'),
        required: true,
        isSecondHalfWidth: true,
        cp: 'input',
      },
      {
        name: 'occupation',
        cp: 'asyncAutoComplete',
        maxLength: 100,
        label: t('job_title'),
        visibleRightIcon: true,
        url: Endpoints.App.Common.getOccupations,
        wrapStyle: classes.formItemWrapStyle,
        normalizer: lookupResponseNormalizer,
        required: true,
      },
      {
        name: 'location',
        apiFunc: geoApi.suggestPlace,
        cp: 'asyncAutoCompleteWithExtraParams',
        label: t('location'),
        wrapStyle: classes.formItemWrapStyle,
        required: true,
        visibleRightIcon: true,
      },
      {
        name: 'form_group_2',
        formGroup: {
          title: t('resume'),
          formSection: true,
          className: classes.formGroupLabel,
        },
        wrapStyle: classes.formItemWrapStyle,
        cp: () => null,
      },
      {
        cp: values.resumeUrl ? ResumePreview : 'resumePicker',
        uploadUrl: candidateEndpoints.uploadResume,
        classNames: {
          addBtn: classes.resumePickerWrapper,
          fileContainer: classes.resumePickerWrapper,
        },
        labels: {
          uploadDate: t('upload_date'),
          uploading: t('file_uploading_3dot'),
          uploadNew: t('upload_new_resume'),
        },
        icons: {
          uploadNew: <ResumePlusIcon />,
        },
        onChange(data: CandidateUploadFileResponse) {
          setFieldValue('resumeUrl', data.value);
        },
      },
    ],
    [t, setFieldValue, values]
  );

  return (
    <ModalTransitionWrapper className={classes.stepWrapper}>
      <DynamicFormBuilder className={classes.formBuilder} groups={formGroups} />
    </ModalTransitionWrapper>
  );
};

export default NewCandidateBody;

function ResumePreview(props: any) {
  const { resumeLink } = props;
  const { values, setFieldValue } = useFormikContext<CreateCandidateFormData>();
  const appDispatch = useGlobalDispatch();

  const openShareViaMessage = () => {
    appDispatch({
      type: 'SET_SHARE_ENTITY_TABBED_MODAL_DATA',
      payload: {
        isOpen: true,
        tabs: [
          ShareEntityTab.COPY_LINK,
          ShareEntityTab.SHARE_VIA_MESSAGE,
          ShareEntityTab.SHARE_VIA_EMAIL,
        ],
        entityData: {
          attachment: {
            type: ShareEntities.RESUME,
            data: {
              type: 'resume',
              link: resumeLink,
              name: resumeLink?.split('/').at(-1), // file name
              fullName: `${values.name} ${values.surname}`,
              user: {
                name: values.name,
              },
            },
          },
        },
      },
    });
  };

  return (
    <Flex className={classes.resumePickerWrapper}>
      <UploadedResumeCard
        name={values.name}
        classNames={{ wrapper: classes.uploadedResumeCard }}
        onShareClick={openShareViaMessage}
        resumeLink={values.resumeUrl}
        showControls
        onDeleteClick={() => {
          setFieldValue('resumeUrl', undefined);
        }}
        onUploadClick={() => {
          setFieldValue('resumeUrl', undefined);
        }}
      />
    </Flex>
  );
}
