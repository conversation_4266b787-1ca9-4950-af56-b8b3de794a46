import type {
  CandidateFormData,
  CreateCandidateAPIRequestBody,
  CreateCandidateFormData,
} from '@shared/types/candidates';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import SubmitButton from '@shared/uikit/Form/SubmitButton';
import {
  closeMultiStepForm,
  openMultiStepForm,
  useMultiStepFormState,
} from 'shared/hooks/useMultiStepForm';
import type { MultiStepFormStepProps } from 'shared/types/generalProps';
import formValidator from 'shared/utils/form/formValidator';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Observer from '@shared/components/atoms/Observer';
import type { CallbackParams, SingleStepItem } from '../../../MultiStepForm';
import { CandidateFormStepKeys } from '../../constants';
import classes from '../../CreateCandidate.module.scss';
import { useCandidateModalContext } from '../../CreateCandidateModalProvider';
import NewCandidateBody from './NewCandidate.body';
import { useDiscardConfirm } from '../../../ProfileSections/sections/utils/useDiscardConfirm';

export default function useNewCandidateStep(): SingleStepItem {
  const { t } = useTranslation();
  const { openConfirm } = useDiscardConfirm();
  const { candidate, Candidate } = useCandidateModalContext();
  const { options } = useMultiStepFormState('createCandidateForm');
  const handleClickDiscard = ({
    dirty,
    resetForm,
  }: Pick<CallbackParams, 'dirty' | 'resetForm'>) => {
    if (dirty) {
      openConfirm(() => {
        resetForm?.();
        closeMultiStepForm('createCandidateForm');
      });
    } else {
      closeMultiStepForm('createCandidateForm');
    }
  };

  const getHeaderProps: MultiStepFormStepProps['getHeaderProps'] = () => ({
    title: t('create_candidate'),
    hideBack: true,
    backButtonProps: {
      onClick: () => closeMultiStepForm('createCandidateForm'),
    },
    noCloseButton: false,
  });

  const getStepHeaderProps: MultiStepFormStepProps['getStepHeaderProps'] =
    () => ({
      title: t('candidate_information'),
      stepKey: CandidateFormStepKeys.NEW,
      iconProps: {
        name: 'candidates-light',
        type: 'fal',
      },
      // helper: '',
    });

  const renderBody: MultiStepFormStepProps['renderBody'] = (props) => (
    <NewCandidateBody {...props} />
  );

  function handleNext(setStep: Function) {
    openMultiStepForm({
      formName: 'createCandidateForm',
      keepPreviousData: false,
      stepKey: CandidateFormStepKeys.GENERAL,
    });
    setStep((step: number) => step + 1);
  }

  const renderFooter: MultiStepFormStepProps['renderFooter'] = ({
    validateForm,
    step,
    setStep,
    dirty,
    resetForm,
  }) => (
    <Flex className={classes.footer}>
      <Observer validateForm={validateForm} step={step} />
      <Button
        fullWidth
        label={t('discard')}
        schema="gray-semi-transparent"
        onClick={() => handleClickDiscard({ dirty, resetForm })}
      />
      {candidate?.id && !dirty ? (
        <Button
          fullWidth
          label={t('next')}
          schema="primary-blue"
          onClick={() => handleNext(setStep)}
        />
      ) : (
        <SubmitButton fullWidth label={t('save')} schema="primary-blue" />
      )}
    </Flex>
  );

  const getValidationSchema = () =>
    formValidator.object().shape({
      email: formValidator
        .string()
        .required('req_email')
        .email('enter_valid_email'),
      name: formValidator.string().required('req_firstName'),
      surname: formValidator.string().required('req_lastName'),
      occupation: formValidator.object().required('req_jobTitle'),
      location: formValidator
        .object()
        .optional()
        .test('value', 'select_one_of_sug_address', (val) => val?.value),
    });

  async function apiFunc(data: any) {
    if (candidate?.id) {
      const editResponse = await Candidate.EditBasic({
        candidateId: candidate?.id,
        body: data,
      });
      return editResponse;
    }
    const responseData = await Candidate.Create({ body: data });
    if (responseData) options?.onCreate?.(responseData);
    return responseData;
  }

  return {
    apiFunc,
    onSuccess: handleNext,
    transform: transformCandidateFormData,
    stepKey: CandidateFormStepKeys.NEW,
    initialValues: candidateBasicInfoNormalizer(candidate),
    getHeaderProps,
    getStepHeaderProps,
    renderBody,
    renderFooter,
    getValidationSchema,
    enableReinitialize: true,
  };
}

export function transformCandidateFormData(
  data: CreateCandidateFormData
): CreateCandidateAPIRequestBody {
  return {
    ...data,
    occupation: data.occupation?.label,
    occupationId: data.occupation?.value,
  };
}

export function candidateBasicInfoNormalizer(
  c?: CandidateFormData
): CreateCandidateFormData | undefined {
  return {
    ...c?.profile,
    resumeUrl: c?.resumeUrl,
    email: c?.profile?.email?.value ?? c?.profile?.email,
  };
}
