import type { CallbackParams } from '@shared/components/Organism/MultiStepForm/MultiStepForm';
import { getSchoolGroups } from '@shared/components/Organism/MultiStepForm/ProfileSections/sections/useSchool';
import ModalTransitionWrapper from '@shared/uikit/Modal/TransitionWrapper/ModalTransitionWrapper';
import useMedia from '@shared/uikit/utils/useMedia';
import useAuthUser from '@shared/utils/hooks/useAuthUser';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useFormikContext } from 'formik';
import classes from '../../../CreateCandidate.module.scss';
import { useGeneralInfoStore } from '../GeneralInfo.store';

const CandidateEducationBody: React.FC<CallbackParams> = () => {
  const { t } = useTranslation();
  const { readonly } = useGeneralInfoStore();
  const { values } = useFormikContext();
  const { isMoreThanTablet } = useMedia();
  const { data } = useAuthUser();
  const countryCode = data?.location?.countryCode;

  return (
    <ModalTransitionWrapper className={classes.stepWrapper}>
      <DynamicFormBuilder
        className={classes.formBuilder}
        groups={getSchoolGroups({
          t,
          isMoreThanTablet,
          values,
          disabledReadOnly: readonly,
          countryCode,
        })}
      />
    </ModalTransitionWrapper>
  );
};

export default CandidateEducationBody;
