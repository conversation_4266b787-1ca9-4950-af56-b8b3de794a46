import { getExperienceGroups } from '@shared/components/Organism/MultiStepForm/ProfileSections/sections/useExperience';
import ModalTransitionWrapper from '@shared/uikit/Modal/TransitionWrapper/ModalTransitionWrapper';
import useMedia from '@shared/uikit/utils/useMedia';
import { useFormikContext } from 'formik';
import type { CallbackParams } from 'shared/components/Organism/MultiStepForm/MultiStepForm';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useAuthUser from '@shared/utils/hooks/useAuthUser';
import classes from '../../../CreateCandidate.module.scss';
import { useGeneralInfoStore } from '../GeneralInfo.store';

const CandidateExperienceBody: React.FC<CallbackParams> = () => {
  const { t } = useTranslation();
  const { readonly } = useGeneralInfoStore();
  const { values } = useFormikContext();
  const { data: authUser } = useAuthUser();
  const countryCode = authUser?.location?.countryCode;

  const { isMoreThanTablet } = useMedia();

  return (
    <ModalTransitionWrapper className={classes.stepWrapper}>
      <DynamicFormBuilder
        className={classes.formRoot}
        groups={getExperienceGroups({
          t,
          values,
          disabledReadOnly: readonly,
          isMoreThanTablet,
          countryCode,
        })}
      />
    </ModalTransitionWrapper>
  );
};

export default CandidateExperienceBody;
