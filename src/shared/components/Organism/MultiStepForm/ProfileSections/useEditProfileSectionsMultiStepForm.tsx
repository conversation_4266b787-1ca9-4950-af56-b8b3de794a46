import useMedia from 'shared/uikit/utils/useMedia';
import useTranslation from 'shared/utils/hooks/useTranslation';
import event from 'shared/utils/toolkit/event';
import eventKeys from 'shared/constants/event-keys';
import {
  closeMultiStepForm,
  openMultiStepForm,
  useMultiStepFormState,
} from 'shared/hooks/useMultiStepForm';
import { type SingleDataItem } from '@shared/components/Organism/MultiStepForm/ProfileSections/types';
import { SectionsList } from './SectionsList';
import { profileSectionsStepKeys } from './constants';
import useBio from './sections/useBio';
import useExperience from './sections/useExperience';
import useVolunteer from './sections/useVolunteer';
import { AccomplishmentsList } from './SectionsList/AccomplishmentsList';
import { EducationList } from './SectionsList/EducationList';
import useSchool from './sections/useSchool';
import useCourse from './sections/useCourse';
import useLicense from './sections/useLicence';
import usePublication from './sections/usePublication';
import useHonor from './sections/useHonor';
import usePatent from './sections/usePatent';
import useSkill from './sections/useSkill';
import useLanguage from './sections/useLanguage';
import useRecommendation from './sections/useRecommendation';
import UploadOrEdit from './UploadOEdit';
import { TwoButtonFooter } from './Components/TwoButtonFooter';
import { UploadedResume } from './SectionsList/UploadedResume';

export function useEditProfileSectionsMultiStepForm(): SingleDataItem[] {
  const { t } = useTranslation();
  const { isMoreThanTablet } = useMedia();
  const state = useMultiStepFormState('editProfileSections');
  const stepKey = state?.stepKey;

  const bioItem = useBio();
  const experienceItem = useExperience();
  const volunteerItem = useVolunteer();
  const schoolItem = useSchool();
  const courseItem = useCourse();
  const licenseItem = useLicense();
  const publicationItem = usePublication();
  const honorItem = useHonor();
  const patentItem = usePatent();
  const skillItem = useSkill();
  const languageItem = useLanguage();
  const recommendationItem = useRecommendation();

  const isNotSingleView = !state?.data?.isSingle;

  const data: Array<SingleDataItem> = [
    (isNotSingleView && {
      stepKey: profileSectionsStepKeys.EDIT_OR_UPLOAD,
      getHeaderProps: () => ({
        title: t('edit_profile'),
        hideBack: isMoreThanTablet,
        backButtonProps: {
          onClick: () => event.trigger(eventKeys.closeModal),
        },
      }),
      renderBody: ({ setStep }) => <UploadOrEdit setStep={setStep} />,
    }) as SingleDataItem,
    (isNotSingleView && {
      stepKey: profileSectionsStepKeys.SECTIONS_LIST,
      getHeaderProps: ({ setStep }) => ({
        title: t('edit_sections'),
        hideBack: false,
        backButtonProps: {
          onClick: () => setStep((prev) => prev - 1),
        },
        noCloseButton: true,
      }),
      renderBody: ({ setStep }) => (
        <SectionsList
          onClick={(stepKey) => {
            openMultiStepForm({ formName: 'editProfileSections', stepKey });
            setStep((prev) => prev + 1);
          }}
        />
      ),
    }) as SingleDataItem,
    (stepKey === profileSectionsStepKeys.EDUCATION && {
      stepKey: profileSectionsStepKeys.EDUCATION,
      getHeaderProps: ({ setStep }) => ({
        title: t('education'),
        hideBack: false,
        backButtonProps: {
          onClick: () => setStep((prev) => prev - 1),
        },
        noCloseButton: true,
      }),
      renderBody: ({ setStep }) => (
        <EducationList
          onClick={(stepKey) => {
            openMultiStepForm({ formName: 'editProfileSections', stepKey });
          }}
        />
      ),
    }) as SingleDataItem,
    (stepKey === profileSectionsStepKeys.ACCOMPLISHMENT && {
      stepKey: profileSectionsStepKeys.ACCOMPLISHMENT,
      getHeaderProps: ({ setStep }) => ({
        title: t('accomplishments'),
        hideBack: false,
        backButtonProps: {
          onClick: () => setStep((prev) => prev - 1),
        },
        noCloseButton: true,
      }),
      renderBody: ({ setStep }) => (
        <AccomplishmentsList
          onClick={(stepKey) => {
            openMultiStepForm({ formName: 'editProfileSections', stepKey });
          }}
        />
      ),
    }) as SingleDataItem,
    (stepKey === profileSectionsStepKeys.RESUME_UPLOADED && {
      stepKey: profileSectionsStepKeys.RESUME_UPLOADED,
      getHeaderProps: () => ({
        title: t('update_profile'),
        hideBack: true,
        noCloseButton: false,
      }),
      renderBody: () => <UploadedResume />,
      renderFooter: () => (
        <TwoButtonFooter
          submitLabel={t('fill_profile')}
          onSubmitClick={() => {
            closeMultiStepForm('editProfileSections');
            setTimeout(
              () =>
                openMultiStepForm({
                  formName: 'resumeUpload',
                  keepPreviousData: true,
                }),
              100
            );
          }}
          secondaryButtonOnClick={() =>
            closeMultiStepForm('editProfileSections')
          }
        />
      ),
    }) as SingleDataItem,

    (stepKey === profileSectionsStepKeys.BIO && bioItem) as SingleDataItem,
    (stepKey === profileSectionsStepKeys.PRO_EXPERIENCE &&
      experienceItem) as SingleDataItem,
    (stepKey === profileSectionsStepKeys.VOL_EXPERIENCE &&
      volunteerItem) as SingleDataItem,
    (stepKey === profileSectionsStepKeys.SCHOOL &&
      schoolItem) as SingleDataItem,
    (stepKey === profileSectionsStepKeys.COURSE &&
      courseItem) as SingleDataItem,
    (stepKey === profileSectionsStepKeys.LICENCE &&
      licenseItem) as SingleDataItem,
    (stepKey === profileSectionsStepKeys.PUBLICATION &&
      publicationItem) as SingleDataItem,
    (stepKey === profileSectionsStepKeys.HONOR && honorItem) as SingleDataItem,
    (stepKey === profileSectionsStepKeys.PATENT &&
      patentItem) as SingleDataItem,
    (stepKey === profileSectionsStepKeys.SKILL && skillItem) as SingleDataItem,
    (stepKey === profileSectionsStepKeys.LANGUAGE &&
      languageItem) as SingleDataItem,
    (stepKey === profileSectionsStepKeys.RECOMMENDATION &&
      recommendationItem) as SingleDataItem,
  ];

  return data?.filter(Boolean) as Array<SingleDataItem>;
}
