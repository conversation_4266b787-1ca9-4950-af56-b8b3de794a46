import React from 'react';
import Endpoints from 'shared/utils/constants/endpoints';
import useTranslation from 'shared/utils/hooks/useTranslation';
import formValidator from 'shared/utils/form/formValidator';

import hereApiResponseNormalizer from 'shared/utils/normalizers/hereApiResponseNormalizer';
import linkValidation from 'shared/utils/form/formValidator/customValidations/linkValidation';
import type Licence from 'shared/types/licence';
import removeBreaksAndSpaces from 'shared/utils/toolkit/removeBreaksAndSpaces';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import cnj from 'shared/uikit/utils/cnj';
import { DESCRIPTION_MAX_LENGTH } from 'shared/constants/enums';
import Flex from 'shared/uikit/Flex';
import { licenceNormalizer } from 'shared/utils/licence.utils';
import HIGHLIGHT_TYPES from 'shared/constants/highlightTypes';
import { type SingleDataItem } from '@shared/components/Organism/MultiStepForm/ProfileSections/types';
import { ViewCard } from '../Components/ViewCard';
import { CombinedFooter } from '../Components/CombinedFooter';
import { getHeaderProps } from './utils/getHeaderProps';
import { useUtils } from './utils/useUtils';
import { profileSectionsStepKeys } from '../constants';
import classes from './styles.module.scss';

const useLicense = (): SingleDataItem => {
  const { t } = useTranslation();

  const {
    data,
    deleteWithConfirm,
    onSuccess,
    handleClick,
    handleBackWithConfirm,
    isSingle,
    deleteItem,
    activeState,
    setActiveState,
    activeItem,
    editUrl,
  } = useUtils({
    url: Endpoints.App.User.Licence.get,
    highlightDataTransformer: (data) => ({
      ...data,
      profileEntityId: data.id,
      subTitle: data.institutionName,
      certificationName: data.title?.label,
      certificationLookupId: data.title?.value,
      type: HIGHLIGHT_TYPES.LICENSE,
      verificationCode: data.verificationCode,
    }),
  });
  const certifications = data?.certifications?.map(licenceNormalizer);

  const initialValues = {
    ...(activeItem || {}),
  };

  return {
    stepKey: profileSectionsStepKeys.LICENCE,
    url: activeState === 'edit' ? editUrl : Endpoints.App.User.Licence.get,
    method: activeState === 'edit' ? 'PUT' : 'POST',
    onSuccess,
    initialValues,
    getValidationSchema: getLicenseValidationSchema,
    transform: transformLicence,
    getHeaderProps: ({ setStep, dirty }) =>
      getHeaderProps({
        dirty,
        setStep,
        activeState,
        handleBackWithConfirm: handleBackWithConfirm(
          profileSectionsStepKeys.EDUCATION
        ),
        titles: {
          main: t('licences_certification'),
          add: t('add_licence'),
          edit: t('edit_licence'),
        },
        isSingle,
      }),
    renderFooter: ({ setStep, dirty }) => (
      <CombinedFooter
        setStep={setStep}
        activeState={activeState}
        deleteWithConfirm={deleteWithConfirm(deleteItem)}
        dirty={dirty}
        handleBackWithConfirm={handleBackWithConfirm(
          profileSectionsStepKeys.EDUCATION
        )}
        setActiveState={setActiveState}
        data={certifications}
        addLabel={t('add_licence')}
      />
    ),
    renderBody: ({ values }) => {
      if (activeState === 'list' && certifications?.length) {
        return (
          <Flex className={classes.listContainer}>
            {certifications?.map((item: any) => (
              <ViewCard
                item={{
                  id: item?.id,
                  image: item?.image,
                  firstText: item?.realData?.title?.label,
                  secondText: item?.realData?.institution?.label,
                  thirdText: item?.secondText,
                  onClick: handleClick(certifications),
                }}
              />
            ))}
          </Flex>
        );
      }
      return (
        <DynamicFormBuilder
          className={classes.formRoot}
          groups={getLicenseGroups({ t })}
        />
      );
    },
  };
};

export default useLicense;

export function getLicenseGroups({ t, isHighlight, countryCode }: any) {
  return [
    {
      name: 'title',
      cp: 'asyncAutoComplete',
      maxLength: 100,
      label: t('title'),
      wrapStyle: cnj(classes.formItem),
      required: true,
      type: 'text',
      visibleRightIcon: true,
      rightIconProps: { name: 'search' },
      url: Endpoints.App.Common.searchCertification,
      normalizer: (data: any) =>
        data.map(({ id: value, title: label }: any) => ({
          label,
          value,
        })),
    },
    {
      name: 'institution',
      cp: 'avatarAsyncAutoComplete',
      isCompany: true,
      maxLength: 100,
      required: true,
      visibleRightIcon: true,
      rightIconProps: { name: 'search' },
      label: t('provider'),
      normalizer: hereApiResponseNormalizer,
      url: `${Endpoints.App.Common.suggestPlace}`,
      params: {
        countryCode,
      },
      wrapStyle: cnj(classes.formItem),
    },
    {
      name: 'startDate',
      cp: 'datePicker',
      wrapStyle: cnj(classes.formItem),
      variant: 'input',
      picker: 'month',
      required: true,
      label: t('issue_date'),
      maxDate: new Date(),
    },
    {
      name: 'verificationCode',
      cp: 'input',
      maxLength: 100,
      label: t('verification_code'),
      wrapStyle: cnj(classes.formItem),
    },
    {
      name: 'certificateLink',
      cp: 'input',
      label: t('link'),
      wrapStyle: cnj(classes.formItem),
    },

    {
      name: 'description',
      visibleOptionalLabel: true,
      cp: 'richtext',
      label: t('description'),
      wrapStyle: cnj(classes.formItem, classes.grow),
      maxLength: DESCRIPTION_MAX_LENGTH,
      className: classes.growingDescription,
      showEmoji: false,
    },
  ].reduce((prev: any, curr) => {
    if (curr.name === 'description' && isHighlight) {
      return prev;
    }
    return [...prev, curr];
  }, []);
}

export const getLicenseValidationSchema = () =>
  formValidator.object().shape({
    certificateLink: linkValidation,
  });

export const transformLicence = ({
  institution,
  title,
  description,
  ...rest
}: Licence) => ({
  ...rest,
  certificationName: title?.label,
  certificationLookupId: title?.value,
  institutionName: institution?.label,
  institutionPageId: `${institution?.value}`?.includes?.('_temp')
    ? null
    : institution?.value,
  pageCroppedImageUrl: institution?.image,
  description: removeBreaksAndSpaces(description),
});
