import React from 'react';
import Endpoints from 'shared/utils/constants/endpoints';
import useTranslation from 'shared/utils/hooks/useTranslation';
import formValidator from 'shared/utils/form/formValidator';

import hereApiResponseNormalizer from 'shared/utils/normalizers/hereApiResponseNormalizer';
import linkValidation from 'shared/utils/form/formValidator/customValidations/linkValidation';
import removeBreaksAndSpaces from 'shared/utils/toolkit/removeBreaksAndSpaces';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import { DESCRIPTION_MAX_LENGTH } from 'shared/constants/enums';
import Flex from 'shared/uikit/Flex';
import { patentNormalizer } from 'shared/utils/userAccomplishment.utils';
import type Patent from 'shared/types/patent';
import cnj from 'shared/uikit/utils/cnj';
import HIGHLIGHT_TYPES from 'shared/constants/highlightTypes';
import descriptionLengthValidator from 'shared/constants/descriptionLengthValidator';
import useAuthUser from '@shared/utils/hooks/useAuthUser';
import { type SingleDataItem } from '@shared/components/Organism/MultiStepForm/ProfileSections/types';
import { useUtils } from './utils/useUtils';
import { getHeaderProps } from './utils/getHeaderProps';
import { CombinedFooter } from '../Components/CombinedFooter';
import { ViewCard } from '../Components/ViewCard';
import { profileSectionsStepKeys } from '../constants';
import classes from './styles.module.scss';

const usePublication = (): SingleDataItem => {
  const { t } = useTranslation();
  const { data: authUser } = useAuthUser();
  const countryCode = authUser?.location?.countryCode;

  const {
    data,
    deleteWithConfirm,
    onSuccess,
    handleClick,
    handleBackWithConfirm,
    isSingle,
    deleteItem,
    activeState,
    setActiveState,
    activeItem,
    editUrl,
  } = useUtils({
    url: Endpoints.App.User.Patent.get,
    highlightDataTransformer: (data) => ({
      ...data,
      startDate: data.date,
      profileEntityId: data.id,
      subTitle: data.institutionName,
      title: data.title,
      link: data.link,
      type: HIGHLIGHT_TYPES.PATENT,
    }),
  });
  const patents = data?.patents?.map(patentNormalizer);

  const initialValues = {
    ...(activeItem || {}),
  };

  return {
    stepKey: profileSectionsStepKeys.PATENT,
    url: activeState === 'edit' ? editUrl : Endpoints.App.User.Patent.get,
    method: activeState === 'edit' ? 'PUT' : 'POST',
    onSuccess,
    initialValues,
    getValidationSchema: getPatentvalidationSchema,
    transform: transformPatent,
    getHeaderProps: ({ setStep, dirty }) =>
      getHeaderProps({
        dirty,
        setStep,
        activeState,
        handleBackWithConfirm: handleBackWithConfirm(
          profileSectionsStepKeys.ACCOMPLISHMENT
        ),
        titles: {
          main: t('patents'),
          add: t('add_patent'),
          edit: t('edit_patent'),
        },
        isSingle,
      }),
    renderFooter: ({ setStep, dirty }) => (
      <CombinedFooter
        setStep={setStep}
        activeState={activeState}
        deleteWithConfirm={deleteWithConfirm(deleteItem)}
        dirty={dirty}
        handleBackWithConfirm={handleBackWithConfirm(
          profileSectionsStepKeys.ACCOMPLISHMENT
        )}
        setActiveState={setActiveState}
        data={patents}
        addLabel={t('add_patent')}
      />
    ),
    renderBody: ({ values }) => {
      if (activeState === 'list' && patents?.length) {
        return (
          <Flex className={classes.listContainer}>
            {patents?.map((item: any) => (
              <ViewCard
                item={{
                  id: item?.id,
                  image: item?.image,
                  firstText: item?.realData?.title,
                  secondText: item?.realData?.institution?.label,
                  thirdText: item?.secondText,
                  onClick: handleClick(patents),
                }}
              />
            ))}
          </Flex>
        );
      }
      return (
        <DynamicFormBuilder
          className={classes.formRoot}
          groups={getPatentGroups({ t, countryCode })}
        />
      );
    },
  };
};

export default usePublication;

export function getPatentGroups({ t, isHighlight = false, countryCode }: any) {
  return [
    {
      name: 'title',
      cp: 'input',
      maxLength: 100,
      label: t('patent_title'),
      helperText: t('patent_title_helper'),
      wrapStyle: classes.formItem,
      required: true,
    },
    {
      name: 'institution',
      cp: 'avatarAsyncAutoComplete',
      isCompany: true,
      maxLength: 100,
      label: t('patent_office'),
      url: `${Endpoints.App.Common.suggestPlace}`,
      params: {
        countryCode,
      },
      normalizer: hereApiResponseNormalizer,
      wrapStyle: cnj(classes.formItem),
      required: true,
      visibleRightIcon: true,
      rightIconProps: { name: 'search' },
    },
    {
      name: 'date',
      cp: 'datePicker',
      variant: 'input',
      wrapStyle: cnj(classes.formItem),
      required: true,
      label: t('obtain_date'),
      maxDate: new Date(),
    },
    {
      name: 'link',
      cp: 'input',
      label: t('link'),
      wrapStyle: cnj(classes.formItem),
      required: true,
      helperText: t('website_helper'),
    },
    {
      name: 'patentId',
      cp: 'input',
      maxLength: 100,
      type: 'number',
      label: t('patent_or_application_number'),
      wrapStyle: cnj(classes.formItem),
    },

    {
      name: 'description',
      visibleOptionalLabel: true,
      cp: 'richtext',
      label: t('description'),
      wrapStyle: cnj(classes.formItem, classes.grow),
      maxLength: DESCRIPTION_MAX_LENGTH,
      className: classes.growingDescription,
      showEmoji: false,
    },
  ].reduce((prev: any, curr) => {
    if (curr.name === 'description' && isHighlight) {
      return prev;
    }
    return [...prev, curr];
  }, []);
}

export const getPatentvalidationSchema = () =>
  formValidator.object().shape({
    link: linkValidation,
    patentId: formValidator.number().nullable().typeError('plz_ent_n_val'),
    description: descriptionLengthValidator,
  });

export const transformPatent = ({
  institution,
  description,
  ...rest
}: Patent) => ({
  ...rest,
  institutionName: institution?.label,
  institutionPageId: `${institution?.value}`?.includes?.('_temp')
    ? null
    : institution?.value,
  pageCroppedImageUrl: institution?.image,
  description: removeBreaksAndSpaces(description),
});
