import React from 'react';
import Endpoints from 'shared/utils/constants/endpoints';
import useTranslation from 'shared/utils/hooks/useTranslation';
import formValidator from 'shared/utils/form/formValidator';

import skillNormalizer from 'shared/utils/normalizers/skillNormalizer';
import { db } from 'shared/utils/constants/enums';
import removeBreaksAndSpaces from 'shared/utils/toolkit/removeBreaksAndSpaces';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import Flex from 'shared/uikit/Flex';
import ProgressItem from 'shared/uikit/ProgressItem';
import PopperMenu from 'shared/uikit/PopperMenu';
import IconButton from 'shared/uikit/Button/IconButton';
import PopperItem from 'shared/uikit/PopperItem';
import HIGHLIGHT_TYPES from 'shared/constants/highlightTypes';
import descriptionLengthValidator from 'shared/constants/descriptionLengthValidator';
import collectionToObjectByKey from '@shared/utils/toolkit/collectionToObjectByKey';
import { type SingleDataItem } from '@shared/components/Organism/MultiStepForm/ProfileSections/types';
import { CheckAndTimesButtons } from '../Components/CheckAndTimesButtons';
import { useUtils } from './utils/useUtils';
import { profileSectionsStepKeys } from '../constants';
import classes from './styles.module.scss';

const useSkill = (): SingleDataItem => {
  const { t } = useTranslation();

  const {
    data,
    deleteWithConfirm,
    onSuccess,
    handleBackWithConfirm,
    isSingle,
    deleteItem,
    activeState,
    setActiveState,
    activeItem,
    setActiveItem,
    editUrl,
  } = useUtils({
    variant: 'onePageOnly',
    url: Endpoints.App.User.Skill.get,
    highlightDataTransformer: (data) => ({
      description: data?.description,
      startDate: data.createdDate,
      profileEntityId: data.id,
      subTitle: data.level,
      title: data.name,
      type: HIGHLIGHT_TYPES.SKILL,
    }),
  });

  const skills = data?.skills?.map(skillNormalizer);

  const initialValues = {
    skills,
    ...(activeItem?.realData || {}),
  };

  const form = (
    <DynamicFormBuilder
      className={classes.formRoot}
      groups={getSkillGroups({
        t,
        setActiveItem,
        setActiveState,
        secondaryDisabled: !skills?.length,
        skills,
      })}
    />
  );

  return {
    stepKey: profileSectionsStepKeys.SKILL,
    url: activeState === 'edit' ? editUrl : Endpoints.App.User.Skill.get,
    method: activeState === 'edit' ? 'PUT' : 'POST',
    onSuccess,
    onFailure: (error, variables, form) => {
      const errorCode = error?.response?.data?.error;
      if (errorCode === 'SkillAlreadyExistInProfileException') {
        form.setErrors({ name: 'SkillAlreadyExistInProfileException' });
      }
    },
    initialValues,
    getValidationSchema: getSkillValidationSchema,
    transform: transformSkill,
    getHeaderProps: ({ setStep, dirty }) => ({
      title: t('skills'),
      hideBack: !!isSingle,
      backButtonProps: {
        onClick: () => handleBackWithConfirm()({ setStep, dirty }),
      },
      noCloseButton: !isSingle,
    }),
    renderBody: ({ values }) => (
      <Flex className={classes.gap}>
        <Flex className={classes.formRoot}>
          {skills?.map((item, index) => {
            const isEditOpen =
              activeState === 'edit' && item?.id === activeItem?.id;

            if (isEditOpen) return form;
            return (
              <ProgressItem
                key={item?.id}
                title={item?.name}
                progressValue={item?.progress}
                tooltipText={t(item?.level)}
                progressSteps={4}
                actionButton={
                  <PopperMenu
                    placement="bottom-end"
                    buttonComponent={
                      <IconButton
                        type="far"
                        name="ellipsis-h"
                        size="sm"
                        colorSchema="transparent"
                      />
                    }
                  >
                    <PopperItem
                      onClick={() => {
                        setActiveItem(item);
                        setActiveState('edit');
                      }}
                      iconName="pen"
                      label={t('edit')}
                    />
                    <PopperItem
                      onClick={() => {
                        setActiveItem(item);
                        deleteWithConfirm(deleteItem)();
                      }}
                      iconName="trash"
                      label={t('remove')}
                    />
                  </PopperMenu>
                }
              />
            );
          })}
        </Flex>

        {(activeState === 'add' || !skills?.length) && form}

        {activeState === 'list' && !!skills?.length && (
          <IconButton
            className={classes.addBtn}
            type="far"
            size="md15"
            name="plus"
            colorSchema="backgroundIconSecondary"
            onClick={() => setActiveState('add')}
          />
        )}
      </Flex>
    ),
  };
};

export default useSkill;

export function getSkillGroups({
  t,
  setActiveItem,
  setActiveState,
  disabledReadOnly,
  secondaryDisabled,
  skills,
}: any) {
  const normalizer = (data) => {
    const skillsObject = collectionToObjectByKey(skills, 'name');
    const result = data.reduce((prev, { id, title, type }) => {
      if (skillsObject[title]) return prev;
      return [...prev, { value: id, label: title, type }];
    }, []);
    return result;
  };

  return [
    {
      name: 'name',
      cp: 'asyncAutoComplete',
      maxLength: 50,
      label: t('skill'),
      url: Endpoints.App.Common.getSkills,
      normalizer,
      required: true,
      disabledReadOnly,
      visibleRightIcon: true,
      rightIconProps: { name: 'search' },
    },
    {
      name: 'level',
      cp: 'dropdownSelect',
      label: t('level'),
      wrapStyle: classes.formItem,
      disabledReadOnly,
      options: db.SKILL_LEVELS,
      required: true,
    },
    {
      name: 'buttons',
      cp: () => (
        <CheckAndTimesButtons
          disabled={disabledReadOnly}
          secondaryButtonOnClick={() => {
            setActiveItem(null);
            setActiveState('list');
          }}
          secondaryDisabled={secondaryDisabled}
        />
      ),
      wrapStyle: classes.formItem,
    },
  ];
}

export const getSkillValidationSchema = () => {
  return formValidator.object().shape({
    level: formValidator
      .object()
      .test(
        'value',
        'SkillAlreadyExistInProfileException',
        (val) => val?.value
      ),
    description: descriptionLengthValidator,
    name: formValidator
      .object()
      .test(
        'no_duplication',
        'SkillAlreadyExistInProfileException',
        (_val, context) => {
          const { name, skills = [] } = context.parent;
          const transformedValue = name?.label?.trim().toLowerCase();
          const duplicatedItems = (skills as Skill[]).find(
            ({ name }) => name?.trim()?.toLowerCase() === transformedValue
          );
          return !duplicatedItems;
        }
      ),
  });
};

export const transformSkill = ({
  name,
  level,
  description,
  ...rest
}: Skill) => ({
  ...rest,
  name: name?.label,
  skillLookupId: name?.value ? +name?.value : undefined,
  level: level?.value,
  type: name?.type,
  description: removeBreaksAndSpaces(description ?? ''),
});
