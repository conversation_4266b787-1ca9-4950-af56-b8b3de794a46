import React, { useEffect, useMemo, useState } from 'react';
import Endpoints from 'shared/utils/constants/endpoints';
import useTranslation from 'shared/utils/hooks/useTranslation';
import formValidator from 'shared/utils/form/formValidator';

import { db } from 'shared/utils/constants/enums';
import useAuthUser from 'shared/utils/hooks/useAuthUser';
import type { UserType } from 'shared/types/user';
import type Recommendation from 'shared/types/recommendation';
import translateReplacer from 'shared/utils/toolkit/translateReplacer';
import event from 'shared/utils/toolkit/event';
import removeBreaksAndSpaces from 'shared/utils/toolkit/removeBreaksAndSpaces';
import uuId from 'shared/utils/toolkit/uuIdGenerator';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import {
  GIVEN_RECOMMENDATION,
  RECEIVED_RECOMMENDATION,
} from 'shared/constants/profileModalsKeys';
import orderBy from 'lodash/orderBy';
import useProfilePage from 'shared/hooks/useProfilePage';
import { recommendationsNormalizer } from 'shared/utils/recommendation.utils';
import RecommendationList from 'shared/components/Organism/RecommendationList/RecommendationList.component';
import cnj from 'shared/uikit/utils/cnj';
import NavLink from '@shared/components/Organism/Tabs/NavLink';
import { DESCRIPTION_MAX_LENGTH } from 'shared/constants/enums';
import IconButton from 'shared/uikit/Button/IconButton';
import { useGetAboutSectionsData } from 'shared/hooks/api-hook';
import useToast from 'shared/uikit/Toast/useToast';
import { useMultiStepFormState } from 'shared/hooks/useMultiStepForm';
import eventKeys from 'shared/constants/event-keys';
import UserPicker from 'shared/uikit/UserPicker';
import ObjectInfoCard from 'shared/components/molecules/ObjectInfoCard';
import useGetUserExperiences from 'shared/hooks/api-hook/useUserExperiences';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import descriptionLengthValidator from 'shared/constants/descriptionLengthValidator';
import { messageTypes } from 'shared/components/Organism/Message/constants';
import { sendWSMessage } from 'shared/utils/message-service';
import { type SingleDataItem } from '@shared/components/Organism/MultiStepForm/ProfileSections/types';
import { TwoButtonFooter } from '../Components/TwoButtonFooter';
import { AddButtonFooter } from '../Components/AddButtonFooter';
import { useUtils } from './utils/useUtils';
import { profileSectionsStepKeys } from '../constants';
import classes from './styles.module.scss';

const useRecommendation = (): SingleDataItem => {
  const { t } = useTranslation();
  const state = useMultiStepFormState('editProfileSections');
  const authUser = useAuthUser();
  const { isAuthUser, objectDetail } = useProfilePage();
  const visitor = objectDetail?.fullName;
  const {
    initialActiveItem,
    initialIsWrite = false,
    initialSelectedUser,
    initialSecondaryStep = 1,
  } = state?.data || {};

  const [isWriteRecommend, setIsWriteRecommend] = useState(initialIsWrite);
  const [isReceived, setIsReceived] = useState(true);
  const [activeSecondaryStep, setActiveSecondaryStep] =
    useState(initialSecondaryStep);
  const [selectedUser, setSelectedUser] = useState<UserType | undefined>(
    initialSelectedUser
  );
  const selectedUsername = selectedUser?.username;
  const toast = useToast();

  const sendMessage = ({
    userId,
    roomId,
    isGroupChat,
    messageOptionalData,
  }: {
    roomId: string;
    isGroupChat?: boolean;
    userId: string;
    messageOptionalData: IMessageOptionalData;
  }) => {
    const wsMessage = {
      text: '',
      type: messageTypes.MESSAGE_TYPE_TEXT,
      id: uuId().create(),
      muc: isGroupChat ? 1 : undefined,
      to: roomId,
      uid: userId,
      data: messageOptionalData,
    };
    sendWSMessage(wsMessage);
  };
  const customizedOnSuccess = (...args) => {
    const data = args[1];
    sendMessage({
      messageOptionalData: {
        recommendation: {
          senderId: data?.sender?.id,
          receiverId: data?.receiver?.id,
          text: data.description || data.askMessage,
          status: data.status,
        },
      },
      roomId: data.status === 'ASKED' ? data?.sender?.id : data?.receiver?.id,
      userId: data.status === 'ASKED' ? data?.receiver?.id : data?.sender?.id,
    });
    toast({
      type: 'success',
      icon: 'check-circle',
      message: t(getMessage()),
    });
  };

  const {
    data,
    onSuccess,
    handleBackWithConfirm,
    isSingle,
    activeState,
    setActiveState,
    activeItem,
    setActiveItem,
    openDiscardConfirm,
  } = useUtils({
    url: '',
    customizedOnSuccess,
  });

  function getMessage() {
    if (isWriteRecommend && activeState === 'edit')
      return translateReplacer(t('you_revised_recommendation'), [
        selectedUser?.fullName || '',
      ]);
    if (!isWriteRecommend && activeState === 'edit')
      return translateReplacer(t('you_asked_revision_from'), [
        selectedUser?.fullName || '',
      ]);
    if (isWriteRecommend && activeState === 'add')
      return translateReplacer(t('your_recom_sent_name'), [
        selectedUser?.fullName || '',
      ]);
    if (!isWriteRecommend && activeState === 'add')
      return translateReplacer(t('your_recomendation_request_sent'), [
        selectedUser?.fullName || '',
      ]);
    return '';
  }

  useEffect(() => {
    if (initialActiveItem) {
      setActiveItem(initialActiveItem);
    }
  }, [initialActiveItem]);

  const { data: experiencesData } = useGetAboutSectionsData({
    username: selectedUser?.username,
  });

  const experiences = experiencesData?.experiences?.map((item) => ({
    value: item.id,
    label: `${item.occupationName} ${t('at')} ${item.companyName}`,
  }));

  const roleAtTheTime = experiences?.find(
    (item) => activeItem?.realData?.roleAtTheTime?.value === item.value
  );

  const initialValues = {
    ...(activeItem?.realData || {}),
    roleAtTheTime,
    description:
      activeState === 'edit' && isWriteRecommend
        ? activeItem?.realData?.description
        : activeState === 'add' && !isWriteRecommend
          ? translateReplacer(t('hi_name_would_y_write_recom'), [
              selectedUser?.name,
            ])
          : undefined,
  };

  const getValidationSchema = () =>
    formValidator.object().shape({
      description: descriptionLengthValidator,
      relationship: formValidator.object().required('this_field_is_required'),
    });

  const transform = ({ relationship, roleAtTheTime, description }: any) => {
    const recommender = selectedUser;

    return {
      recommender: {
        fullName: isWriteRecommend
          ? authUser?.data?.fullName
          : recommender?.fullName,
      },
      fromUserId: isWriteRecommend ? authUser?.data?.id : recommender?.id,
      receiverUserId: isWriteRecommend ? recommender?.id : authUser?.data?.id,
      description: removeBreaksAndSpaces(description),
      askMessage: removeBreaksAndSpaces(description),
      relationship: relationship?.value,
      roleAtTheTime: roleAtTheTime?.value,
    };
  };

  const onFailure = ({ response }) => {
    toast({
      type: 'error',
      icon: 'times-circle',
      message:
        response?.data?.error === 'DuplicateRecommendationForSameRoleException'
          ? t('DuplicateRecommendationForSameRoleException')
          : t('something_went_wrong'),
    });
  };

  const receivedRecommendations = data?.receivedRecommendations?.map((item) =>
    recommendationsNormalizer(item, RECEIVED_RECOMMENDATION, authUser?.id, t)
  );
  const givenRecommendations = data?.givenRecommendations?.map((item) =>
    recommendationsNormalizer(item, GIVEN_RECOMMENDATION, authUser?.id, t)
  );
  const sortedReceivedRecommendations = useMemo(
    () => orderBy(receivedRecommendations, ['lastModifiedDate'], ['desc']),
    [receivedRecommendations]
  );

  const sortedGivenRecommendations = useMemo(
    () => orderBy(givenRecommendations, ['lastModifiedDate'], ['desc']),
    [givenRecommendations]
  );

  const recommendationListClicks = {
    onAskForRevision: (recom: Partial<{ realData: Recommendation }>) => {
      setIsWriteRecommend(false);
      setActiveItem(recom);
      setSelectedUser(recom?.realData?.sender as UserType);
      setActiveState('edit');
      setActiveSecondaryStep(2);
    },
    onWriteRecommendation: (recom: Partial<{ realData: Recommendation }>) => {
      setIsWriteRecommend(true);
      setActiveItem(recom);
      setSelectedUser(recom?.realData?.receiver as UserType);
      setActiveState('add');
      setActiveSecondaryStep(2);
    },
    onWriteRevision: (recom: Partial<{ realData: Recommendation }>) => {
      setIsWriteRecommend(true);
      setActiveItem(recom);
      setSelectedUser(recom?.realData?.receiver as UserType);
      setActiveState('edit');
      setActiveSecondaryStep(2);
    },
  };

  const renderListBody = () => (
    <Flex>
      {isReceived ? (
        <>
          {sortedReceivedRecommendations?.length === 0 ? (
            <Typography size={14} height={18} isWordWrap>
              {isAuthUser
                ? t('you_empty_received_recom')
                : `${visitor} ${t('empty_received_recommendation')}`}
            </Typography>
          ) : (
            <RecommendationList
              tab="received"
              data={sortedReceivedRecommendations}
              noLimit
              {...recommendationListClicks}
              isInWideRightSideModal
            />
          )}
        </>
      ) : (
        <>
          {sortedGivenRecommendations?.length === 0 ? (
            <Typography size={14} height={18}>
              {isAuthUser
                ? t('you_empty_given_recom')
                : `${visitor} ${t('empty_given_recommendation')}`}
            </Typography>
          ) : (
            <RecommendationList
              tab="given"
              data={sortedGivenRecommendations}
              noLimit
              {...recommendationListClicks}
              isInWideRightSideModal
            />
          )}
        </>
      )}
    </Flex>
  );

  const roleAtTheTimeField = useRoleAtTheTimeField({
    isWriteRecommend,
    selectedUsername,
  });

  useEffect(() => {
    if (activeState === 'list') {
      // reset
      setSelectedUser(initialSelectedUser);
      setActiveSecondaryStep(initialSecondaryStep);
      setIsWriteRecommend(initialIsWrite);
    }
  }, [activeState]);

  const renderUserList = ({}) => (
    <UserPicker
      placeholder={t('search_profile')}
      userType="PERSON"
      hideCounter
      customAction={() => (
        <IconButton
          name="chevron-right"
          type="far"
          size="md20"
          colorSchema="transparent1"
          className={classes.checkIcon}
        />
      )}
      onChange={(item: UserType) => {
        setSelectedUser(item);
        setActiveSecondaryStep(2);
      }}
      classNames={{
        listWrapper: classes.userListWrapper,
        list: classes.userList,
        searchInput: classes.searchInput,
      }}
      value={selectedUser}
    />
  );

  const renderFormBody = ({ values }: any) => (
    <DynamicFormBuilder
      key={String(isWriteRecommend)}
      className={classes.formRoot}
      groups={[
        {
          name: 'advanced-avatar',
          wrapStyle: cnj(classes.formItem, classes.marginBottom),
          cp: () => (
            <ObjectInfoCard
              isFirstTextSmall
              firstText={selectedUser?.fullName}
              secondText={selectedUser?.usernameAtSign}
              thirdText={
                selectedUser?.occupationName || selectedUser?.occupation?.label
              }
              fourthText={selectedUser?.location?.title}
              avatar={selectedUser?.croppedImageUrl}
            />
          ),
        },
        {
          name: 'relationship',
          cp: 'dropdownSelect',
          label: t('relationship'),
          options: db.RELATIONSHIP_OPTIONS?.(selectedUser?.name, undefined, t),
          required: true,
          visibleOptionalLabel: false,
          wrapStyle: cnj(classes.formItem),
          disabledReadOnly: activeState !== 'add',
          visibleRightIcon: true,
          rightIconProps: {
            colorSchema: 'transparent',
          },
        },
        !!roleAtTheTimeField && {
          ...roleAtTheTimeField,
          wrapStyle: cnj(classes.formItem),
          disabledReadOnly: activeState !== 'add',
          rightIconProps: {
            colorSchema: 'transparent',
          },
          required: false,
        },
        {
          name: 'description',
          cp: 'richtext',
          wrapStyle: cnj(classes.formItem, classes.grow),
          label: isWriteRecommend
            ? t('your_recommendation')
            : t('your_message'),
          required: true,
          maxLength: DESCRIPTION_MAX_LENGTH,
          className: classes.growingDescription,
          showEmoji: false,
          placeholder: 'salasdasdasd',
        },
      ].filter(Boolean)}
    />
  );

  function getHeaderBelowContent({ resetBody }: { resetBody: any }) {
    if (activeSecondaryStep !== 1 && !initialSelectedUser) return;
    return (
      <Flex className={cnj(classes.linksWrap)}>
        {activeState === 'list' ? (
          <>
            <NavLink
              isActive={isReceived}
              isFullWidth
              title={`${t('received')} (${sortedReceivedRecommendations?.length})`}
              onClick={() => {
                setIsReceived(true);
                resetBody();
              }}
            />
            <NavLink
              isActive={!isReceived}
              isFirstLink
              isFullWidth
              title={`${t('given')} (${sortedGivenRecommendations?.length})`}
              onClick={() => {
                setIsReceived(false);
                resetBody();
              }}
            />
          </>
        ) : (
          <>
            <NavLink
              isActive={!isWriteRecommend}
              isFirstLink
              isFullWidth
              title={t('REQUEST')}
              onClick={() => {
                setIsWriteRecommend(false);
                resetBody();
              }}
            />
            <NavLink
              isActive={isWriteRecommend}
              isFullWidth
              title={t('write_cap')}
              onClick={() => {
                setIsWriteRecommend(true);
                resetBody();
              }}
            />
          </>
        )}
      </Flex>
    );
  }

  function getTitle() {
    if (activeSecondaryStep === 1) return t('recommendations');
    if (activeState === 'edit' && isWriteRecommend) return t('revise_recom');
    if (activeState === 'edit' && !isWriteRecommend)
      return t('ask_for_revision');
    if (activeState === 'add' && isWriteRecommend)
      return t('write_recommendation');
    if (activeState === 'add' && !isWriteRecommend)
      return t('REQUEST_RECOMMENDATION');
    return t('recommendations');
  }

  function getUrl() {
    if (activeState === 'edit' && isWriteRecommend)
      return Endpoints.App.User.Recommendation.writeRecommend(activeItem?.id);
    if (activeState === 'edit' && !isWriteRecommend)
      return Endpoints.App.User.Recommendation.revision(activeItem?.id);
    if (activeState === 'add' && isWriteRecommend)
      return Endpoints.App.User.Recommendation.recommend;
    if (activeState === 'add' && !isWriteRecommend)
      return Endpoints.App.User.Recommendation.ask;
    return '';
  }

  function getHideBack() {
    if (isSingle && activeState === 'add' && initialSecondaryStep === 2) {
      return true;
    }
    if (
      isSingle &&
      activeState === 'add' &&
      initialSecondaryStep === 1 &&
      activeSecondaryStep === 2
    ) {
      return false;
    }
    return !!isSingle;
  }
  const handleback =
    ({ setStep, dirty }: any) =>
    () => {
      const func = () => {
        setActiveSecondaryStep(1);
        setSelectedUser(undefined);
        event.trigger(eventKeys.resetForm);
      };

      if (
        isSingle &&
        activeState === 'add' &&
        initialSecondaryStep === 1 &&
        activeSecondaryStep === 2
      ) {
        if (dirty) {
          openDiscardConfirm(func);
        } else {
          func();
        }
      } else if (
        !isSingle &&
        activeState === 'add' &&
        activeSecondaryStep === 2
      ) {
        if (dirty) {
          openDiscardConfirm(func);
        } else {
          func();
        }
      } else {
        handleBackWithConfirm(undefined, () => setIsReceived(true))({
          setStep,
          dirty,
        });
      }
    };

  return {
    stepKey: profileSectionsStepKeys.RECOMMENDATION,
    url: getUrl(),
    method: activeState === 'edit' ? 'PUT' : 'POST',
    onSuccess,
    onFailure,
    initialValues,
    getValidationSchema,
    transform,
    getHeaderProps: ({ setStep, dirty, resetBody }) => ({
      title: getTitle(),
      hideBack: getHideBack(),
      backButtonProps: {
        onClick: handleback({ setStep, dirty }),
      },
      noCloseButton: !getHideBack(),
      belowContent: getHeaderBelowContent({ resetBody }),
    }),
    renderFooter: ({ setStep, dirty }) =>
      activeState === 'list' ? (
        <AddButtonFooter
          label={t('add_recommendation')}
          onClick={() => setActiveState('add')}
        />
      ) : activeSecondaryStep === 1 ? undefined : (
        <TwoButtonFooter
          submitLabel={t('send')}
          secondaryButtonOnClick={handleback({ setStep, dirty })}
        />
      ),
    renderBody:
      activeState === 'list'
        ? renderListBody
        : activeSecondaryStep === 1
          ? renderUserList
          : renderFormBody,
  };
};

export default useRecommendation;

const useRoleAtTheTimeField = ({ isWriteRecommend, selectedUsername }: any) => {
  const { t } = useTranslation();
  const { authUser } = useGetAppObject();

  const { data: selectedUserData, isLoading: selectedUserIsLoading } =
    useGetUserExperiences({
      username: selectedUsername,
      isEnabled: !!selectedUsername,
    });

  const { data } = useGetAboutSectionsData({
    username: authUser.username,
  });

  const isNotExperiencesEmpty = isWriteRecommend
    ? selectedUserData?.length > 0
    : data?.experiences?.length > 0;
  const experiences = [
    ...(isWriteRecommend ? selectedUserData || [] : data?.experiences || []),
  ]?.map((item) => ({
    value: item.id,
    label: `${item.occupationName} ${t('at')} ${item.companyName}`,
  }));

  return isNotExperiencesEmpty
    ? {
        name: 'roleAtTheTime',
        cp: 'dropdownSelect',
        label:
          authUser?.username === selectedUsername
            ? t('your_role_at_the_time')
            : t('role_at_the_time'),
        wrapStyle: 'responsive-margin-top',
        options: experiences,
        required: true,
      }
    : undefined;
};
