import React from 'react';
import Endpoints from 'shared/utils/constants/endpoints';
import useTranslation from 'shared/utils/hooks/useTranslation';
import formValidator from 'shared/utils/form/formValidator';

import isEmptyObjectValues from 'shared/utils/toolkit/isEmptyObjectValues';
import lookupResponseNormalizer from 'shared/utils/normalizers/lookupResponseNormalizer';
import hereApiResponseNormalizer from 'shared/utils/normalizers/hereApiResponseNormalizer';
import geoApi from 'shared/utils/api/geo';
import { db } from 'shared/utils/constants/enums';
import removeBreaksAndSpaces from 'shared/utils/toolkit/removeBreaksAndSpaces';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import cnj from 'shared/uikit/utils/cnj';
import { DESCRIPTION_MAX_LENGTH } from 'shared/constants/enums';
import Flex from 'shared/uikit/Flex';
import { educationNormalizer } from 'shared/utils/education.utils';
import useMedia from 'shared/uikit/utils/useMedia';
import HIGHLIGHT_TYPES from 'shared/constants/highlightTypes';
import descriptionLengthValidator from 'shared/constants/descriptionLengthValidator';
import useAuthUser from '@shared/utils/hooks/useAuthUser';
import { type SingleDataItem } from '@shared/components/Organism/MultiStepForm/ProfileSections/types';
import { useUtils } from './utils/useUtils';
import { getHeaderProps } from './utils/getHeaderProps';
import { CombinedFooter } from '../Components/CombinedFooter';
import { ViewCard } from '../Components/ViewCard';
import { profileSectionsStepKeys } from '../constants';
import classes from './styles.module.scss';

const useSchool = (isVolunteer = false): SingleDataItem => {
  const { t } = useTranslation();
  const { data: authUser } = useAuthUser();
  const countryCode = authUser?.location?.countryCode;

  const { isMoreThanTablet } = useMedia();

  const {
    data,
    deleteWithConfirm,
    onSuccess,
    handleClick,
    handleBackWithConfirm,
    isSingle,
    deleteItem,
    activeState,
    setActiveState,
    activeItem,
    editUrl,
  } = useUtils({
    url: Endpoints.App.User.Education.get,
    highlightDataTransformer: (data) => ({
      ...data,
      profileEntityId: data.id,
      subTitle: data.majorName,
      title: data.schoolName,
      type: HIGHLIGHT_TYPES.SCHOOL,
    }),
  });

  const educations = data?.educations?.map((item) =>
    educationNormalizer(item, t)
  );

  const initialValues = {
    volunteer: isVolunteer,
    ...(activeItem || {}),
  };

  return {
    stepKey: profileSectionsStepKeys.SCHOOL,
    url: activeState === 'edit' ? editUrl : Endpoints.App.User.Education.get,
    method: activeState === 'edit' ? 'PUT' : 'POST',
    onSuccess,
    initialValues,
    getValidationSchema: getSchoolValidationSchema,
    transform: transformSchool,
    getHeaderProps: ({ setStep, dirty }) =>
      getHeaderProps({
        dirty,
        setStep,
        activeState,
        handleBackWithConfirm: handleBackWithConfirm(
          profileSectionsStepKeys.EDUCATION
        ),
        titles: {
          main: t('schools'),
          add: t('add_school'),
          edit: t('edit_school'),
        },
        isSingle,
      }),
    renderFooter: ({ setStep, dirty }) => (
      <CombinedFooter
        setStep={setStep}
        activeState={activeState}
        deleteWithConfirm={deleteWithConfirm(deleteItem)}
        dirty={dirty}
        handleBackWithConfirm={handleBackWithConfirm(
          profileSectionsStepKeys.EDUCATION
        )}
        setActiveState={setActiveState}
        data={educations}
        addLabel={t('add_school')}
      />
    ),
    renderBody: ({ values }) => {
      if (activeState === 'list' && educations?.length) {
        return (
          <Flex className={classes.listContainer}>
            {educations?.map((item: any) => (
              <ViewCard
                key={item.id}
                item={{
                  id: item?.id,
                  image: item?.image,
                  firstText: item?.realData?.major?.label,
                  secondText: item?.realData?.school?.label,
                  thirdText: item?.secondText,
                  onClick: handleClick(educations),
                }}
              />
            ))}
          </Flex>
        );
      }
      return (
        <DynamicFormBuilder
          className={classes.formRoot}
          groups={getSchoolGroups({ t, isMoreThanTablet, values, countryCode })}
        />
      );
    },
  };
};

export default useSchool;

export function getSchoolGroups({
  t,
  isMoreThanTablet,
  values,
  isHighlight = false,
  disabled = false,
  isEdit = false,
  disabledReadOnly,
  countryCode,
}: any) {
  return [
    {
      name: 'school',
      cp: 'avatarAsyncAutoComplete',
      isCompany: true,
      wrapStyle: cnj(classes.formItem),
      maxLength: 100,
      label: t('school'),
      url: `${Endpoints.App.Common.suggestPlace}`,
      normalizer: hereApiResponseNormalizer,
      disabledReadOnly,
      required: true,
      visibleRightIcon: true,
      rightIconProps: { name: 'search' },
      params: {
        countryCode,
      },
    },
    {
      name: 'major',
      cp: 'asyncAutoComplete',
      maxLength: 100,
      label: t('major'),
      normalizer: lookupResponseNormalizer,
      disabledReadOnly,
      url: Endpoints.App.Common.getMajors,
      wrapStyle: cnj(classes.formItem),

      required: true,
      visibleRightIcon: true,
      rightIconProps: { name: 'search' },
    },
    {
      name: 'location',
      apiFunc: geoApi.suggestCity,
      label: t('location'),
      wrapStyle: cnj(classes.formItem),
      disabledReadOnly,
      visibleRightIcon: true,
      rightIconProps: { name: 'search' },
      cp: 'asyncAutoCompleteWithExtraParams',
      onlyChooseFromOptions: true,
    },

    {
      name: 'degree',
      cp: 'dropdownSelect',
      label: t('degree'),
      wrapStyle: classes.formItem,
      disabledReadOnly,
      options: db.DEGREE_OPTIONS,
    },
    {
      name: 'currentlyStudying',
      cp: 'checkBox',
      label: t('currently_studying_here'),
      wrapStyle: cnj(classes.formItem),
      disabledReadOnly,
      disabled: disabledReadOnly,
      visibleOptionalLabel: false,
    },
    {
      name: 'startDate',
      cp: 'datePicker',
      variant: 'input',
      wrapStyle: cnj(classes.formItem, isMoreThanTablet && classes.grow),
      required: true,
      picker: 'month',
      label: t('start_date'),
      isFirstHalfWidth: isMoreThanTablet && true,
      disabledReadOnly,
      rowContainerClassName: classes.rowContainerClassName,
      maxDate: new Date(),
      halfWidthWhenOpen: true,
    },
    {
      name: 'endDate',
      cp: 'datePicker',
      wrapStyle: cnj(classes.formItem, isMoreThanTablet && classes.grow),
      variant: 'input',
      picker: 'month',
      visibleOptionalLabel: false,
      minDate: values?.startDate ? new Date(values?.startDate) : undefined,
      label: t('end_date'),
      disabledReadOnly,
      textInputProps: {
        disabled: values?.currentlyStudying,
      },
      required: false,
      isSecondHalfWidth: isMoreThanTablet && true,
      halfWidthWhenOpen: true,
    },
    {
      name: 'description',
      visibleOptionalLabel: true,
      cp: 'richtext',
      label: t('description'),
      wrapStyle: cnj(classes.formItem, classes.grow),
      maxLength: DESCRIPTION_MAX_LENGTH,
      disabledReadOnly,
      className: classes.growingDescription,
      showEmoji: false,
    },
  ].reduce((prev: any, curr) => {
    if (isHighlight && curr.name === 'description') {
      return prev;
    }
    if (curr.name === 'share' && isEdit) {
      return prev;
    }
    return [...prev, curr];
  }, []);
}
export const getSchoolValidationSchema = () =>
  formValidator.object().shape({
    location: formValidator
      .object()
      .test(
        'value',
        'select_one_of_sug_locns',
        (val: any) => isEmptyObjectValues(val) || val?.value
      ),
    endDate: formValidator
      .date()
      .typeError('this_field_is_required')
      .when(
        ['startDate', 'currentlyStudying'],
        (startDate, currentlyStudying, schema) => {
          if (currentlyStudying) {
            return schema.nullable();
          }
          return startDate
            ? schema
                .min(startDate, 'date_e_b_s')
                .required('this_field_is_required')
            : schema.nullable();
        }
      ),
    description: descriptionLengthValidator,
  });

export const transformSchool = ({
  school: selectedSchool,
  degree,
  major,
  currentlyStudying,
  endDate,
  description,
  ...rest
}: any) => ({
  ...rest,
  currentlyStudying,
  endDate: currentlyStudying ? undefined : endDate,
  schoolName: selectedSchool?.label,
  schoolPageId: `${selectedSchool?.value}`?.includes?.('_temp')
    ? null
    : selectedSchool?.value,
  pageCroppedImageUrl: selectedSchool?.image,
  degree: degree?.value,
  majorLookupId: major?.value,
  majorName: major?.label,
  description: removeBreaksAndSpaces(description),
});
