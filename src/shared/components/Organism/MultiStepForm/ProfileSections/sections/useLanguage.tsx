import React from 'react';
import Endpoints from 'shared/utils/constants/endpoints';
import useTranslation from 'shared/utils/hooks/useTranslation';
import formValidator from 'shared/utils/form/formValidator';

import { db } from 'shared/utils/constants/enums';
import languageNormalizer from 'shared/utils/normalizers/languageNormalizer';
import type { Language } from 'shared/types/language';
import removeBreaksAndSpaces from 'shared/utils/toolkit/removeBreaksAndSpaces';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import Flex from 'shared/uikit/Flex';
import ProgressItem from 'shared/uikit/ProgressItem';
import PopperMenu from 'shared/uikit/PopperMenu';
import IconButton from 'shared/uikit/Button/IconButton';
import PopperItem from 'shared/uikit/PopperItem';
import HIGHLIGHT_TYPES from 'shared/constants/highlightTypes';
import descriptionLengthValidator from 'shared/constants/descriptionLengthValidator';
import collectionToObjectByKey from '@shared/utils/toolkit/collectionToObjectByKey';
import { type SingleDataItem } from '@shared/components/Organism/MultiStepForm/ProfileSections/types';
import { CheckAndTimesButtons } from '../Components/CheckAndTimesButtons';
import { useUtils } from './utils/useUtils';
import { profileSectionsStepKeys } from '../constants';
import classes from './styles.module.scss';

const useLanguage = (): SingleDataItem => {
  const { t } = useTranslation();

  const {
    data,
    deleteWithConfirm,
    onSuccess,
    handleBackWithConfirm,
    isSingle,
    deleteItem,
    activeState,
    setActiveState,
    activeItem,
    setActiveItem,
    editUrl,
  } = useUtils({
    variant: 'onePageOnly',
    url: Endpoints.App.User.Language.get,
    highlightDataTransformer: (data, values) => ({
      description: values?.description,
      startDate: data.createdDate,
      profileEntityId: data.id,
      subTitle: data.level,
      title: data.name,
      type: HIGHLIGHT_TYPES.LANGUAGE,
    }),
  });

  const languages = data?.languages?.map(languageNormalizer);

  const initialValues = {
    ...(activeItem?.realData || {}),
  };

  const form = (() => (
    <DynamicFormBuilder
      className={classes.formRoot}
      groups={getLanguageGroups({
        t,
        setActiveItem,
        setActiveState,
        secondaryDisabled: !languages?.length,
        languages,
      })}
    />
  ))();

  return {
    stepKey: profileSectionsStepKeys.LANGUAGE,
    url: activeState === 'edit' ? editUrl : Endpoints.App.User.Language.get,
    method: activeState === 'edit' ? 'PUT' : 'POST',
    onSuccess,
    initialValues,
    getValidationSchema: getLanguageValidationSchema,
    transform: transformLanguage,
    getHeaderProps: ({ setStep, dirty }) => ({
      title: t('languages'),
      hideBack: !!isSingle,
      backButtonProps: {
        onClick: () => handleBackWithConfirm()({ setStep, dirty }),
      },
      noCloseButton: !isSingle,
    }),
    renderBody: ({ values }) => (
      <Flex className={classes.gap}>
        <Flex className={classes.formRoot}>
          {languages?.map((item, index) => {
            const isEditOpen =
              activeState === 'edit' && item?.id === activeItem?.id;

            if (isEditOpen) return form;
            return (
              <ProgressItem
                key={item?.id}
                title={item?.name}
                progressValue={item?.progress}
                tooltipText={t(item?.level)}
                progressSteps={7}
                actionButton={
                  <PopperMenu
                    placement="bottom-end"
                    buttonComponent={
                      <IconButton
                        type="far"
                        name="ellipsis-h"
                        size="sm"
                        colorSchema="transparent"
                      />
                    }
                  >
                    <PopperItem
                      onClick={() => {
                        setActiveItem(item);
                        setActiveState('edit');
                      }}
                      iconName="pen"
                      label={t('edit')}
                    />
                    <PopperItem
                      onClick={() => {
                        setActiveItem(item);
                        deleteWithConfirm(deleteItem)();
                      }}
                      iconName="trash"
                      label={t('remove')}
                    />
                  </PopperMenu>
                }
              />
            );
          })}
        </Flex>
        {(activeState === 'add' || !languages?.length) && form}
        {activeState === 'list' && !!languages?.length && (
          <IconButton
            className={classes.addBtn}
            type="far"
            size="md15"
            name="plus"
            colorSchema="backgroundIconSecondary"
            onClick={() => setActiveState('add')}
          />
        )}
      </Flex>
    ),
  };
};

export default useLanguage;

export function getLanguageGroups({
  t,
  setActiveItem,
  setActiveState,
  disabledReadOnly,
  secondaryDisabled,
  languages,
}: any) {
  const normalizer = (data) => {
    const languagesObject = collectionToObjectByKey(languages, 'name');
    const result = data.reduce((prev, { id, title, type }) => {
      if (languagesObject[title]) return prev;
      return [...prev, { value: id, label: title, type }];
    }, []);
    return result;
  };
  return [
    {
      name: 'name',
      cp: 'asyncAutoComplete',
      // maxLength: 100,
      label: t('language'),
      url: Endpoints.App.Common.getLanguages,
      normalizer,
      disabledReadOnly,
      required: true,
      visibleRightIcon: true,
      rightIconProps: { name: 'search' },
    },
    {
      name: 'level',
      cp: 'dropdownSelect',
      label: t('level'),
      wrapStyle: classes.formItem,
      disabledReadOnly,
      options: db.LANGUAGE_LEVELS,
      required: true,
    },
    {
      name: 'buttons',
      cp: () => (
        <CheckAndTimesButtons
          disabled={disabledReadOnly}
          secondaryButtonOnClick={() => {
            setActiveItem(null);
            setActiveState('list');
          }}
          secondaryDisabled={secondaryDisabled}
        />
      ),
      wrapStyle: classes.formItem,
    },
  ];
}

export const getLanguageValidationSchema = () =>
  formValidator.object().shape({
    name: formValidator
      .object()
      .test('value', 'select_one_of_sug_langs', (val) => val?.value),
    level: formValidator
      .object()
      .when('name', (name: any, schema: any) =>
        name?.value ? schema.required('required_level') : schema
      ),
    description: descriptionLengthValidator,
  });

export const transformLanguage = ({
  name,
  level,
  description,
  ...rest
}: Language) => ({
  ...rest,
  name: name?.label,
  languageLookupId: name?.value,
  level: level?.value,
  description: removeBreaksAndSpaces(description),
});
