import React from 'react';
import Endpoints from 'shared/utils/constants/endpoints';
import useTranslation from 'shared/utils/hooks/useTranslation';
import formValidator from 'shared/utils/form/formValidator';
import hereApiResponseNormalizer from 'shared/utils/normalizers/hereApiResponseNormalizer';
import linkValidation from 'shared/utils/form/formValidator/customValidations/linkValidation';
import removeBreaksAndSpaces from 'shared/utils/toolkit/removeBreaksAndSpaces';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import cnj from 'shared/uikit/utils/cnj';
import { DESCRIPTION_MAX_LENGTH } from 'shared/constants/enums';
import Flex from 'shared/uikit/Flex';
import { awardNormalizer } from 'shared/utils/userAccomplishment.utils';
import type Award from 'shared/utils/Award';
import HIGHLIGHT_TYPES from 'shared/constants/highlightTypes';
import descriptionLengthValidator from 'shared/constants/descriptionLengthValidator';
import useAuthUser from '@shared/utils/hooks/useAuthUser';
import { type SingleDataItem } from '@shared/components/Organism/MultiStepForm/ProfileSections/types';
import { useUtils } from './utils/useUtils';
import { getHeaderProps } from './utils/getHeaderProps';
import { CombinedFooter } from '../Components/CombinedFooter';
import { ViewCard } from '../Components/ViewCard';
import { profileSectionsStepKeys } from '../constants';
import classes from './styles.module.scss';

const useHonor = (): SingleDataItem => {
  const { t } = useTranslation();
  const { data: authUser } = useAuthUser();
  const countryCode = authUser?.location?.countryCode;

  const {
    data,
    deleteWithConfirm,
    onSuccess,
    handleClick,
    handleBackWithConfirm,
    isSingle,
    deleteItem,
    activeState,
    setActiveState,
    activeItem,
    editUrl,
  } = useUtils({
    url: Endpoints.App.User.Honor.get,
    highlightDataTransformer: (data) => ({
      description: data?.description,
      link: data.link,
      startDate: data.date,
      profileEntityId: data.id,
      subTitle: data.institutionName,
      title: data.awardTitle,
      type: HIGHLIGHT_TYPES.AWARD,
    }),
  });
  const honors = data?.awards?.map(awardNormalizer);

  const initialValues = {
    ...(activeItem || {}),
  };

  return {
    stepKey: profileSectionsStepKeys.HONOR,
    url: activeState === 'edit' ? editUrl : Endpoints.App.User.Honor.get,
    method: activeState === 'edit' ? 'PUT' : 'POST',
    onSuccess,
    initialValues,
    getValidationSchema: getHonorValidationSchema,
    transform: transformHonor,
    getHeaderProps: ({ setStep, dirty }) =>
      getHeaderProps({
        dirty,
        setStep,
        activeState,
        handleBackWithConfirm: handleBackWithConfirm(
          profileSectionsStepKeys.ACCOMPLISHMENT
        ),
        titles: {
          main: t('awards'),
          add: t('add_awards'),
          edit: t('edit_awards'),
        },
        isSingle,
      }),
    renderFooter: ({ setStep, dirty }) => (
      <CombinedFooter
        setStep={setStep}
        activeState={activeState}
        deleteWithConfirm={deleteWithConfirm(deleteItem)}
        dirty={dirty}
        handleBackWithConfirm={handleBackWithConfirm(
          profileSectionsStepKeys.ACCOMPLISHMENT
        )}
        setActiveState={setActiveState}
        data={honors}
        addLabel={t('add_awards')}
      />
    ),
    renderBody: ({ values }) => {
      if (activeState === 'list' && honors?.length) {
        return (
          <Flex className={classes.listContainer}>
            {honors?.map((item: any) => (
              <ViewCard
                item={{
                  id: item?.id,
                  image: item?.image,
                  firstText: item?.realData?.title?.label,
                  secondText: item?.realData?.institution?.label,
                  thirdText: item?.secondText,
                  onClick: handleClick(honors),
                }}
              />
            ))}
          </Flex>
        );
      }
      return (
        <DynamicFormBuilder
          className={classes.formRoot}
          groups={getHonorGroups({ t, countryCode })}
        />
      );
    },
  };
};

export default useHonor;

export function getHonorGroups({ t, isHighlight = false, countryCode }: any) {
  return [
    {
      name: 'title',
      label: t('title'),
      type: 'text',
      maxLength: 100,
      required: true,
      cp: 'asyncAutoComplete',
      visibleRightIcon: true,
      rightIconProps: { name: 'search' },
      url: Endpoints.App.Common.searchAward,
      normalizer: (data: any) =>
        data.map(({ id: value, title: label }: any) => ({
          label,
          value,
        })),
      wrapStyle: cnj(classes.formItem),
    },
    {
      name: 'institution',
      cp: 'avatarAsyncAutoComplete',
      isCompany: true,
      maxLength: 100,
      label: t('presented_by'),
      visibleRightIcon: true,
      rightIconProps: { name: 'search' },
      required: true,
      url: `${Endpoints.App.Common.suggestPlace}`,
      params: {
        countryCode,
      },
      normalizer: hereApiResponseNormalizer,
      wrapStyle: cnj(classes.formItem),
    },
    {
      name: 'date',
      cp: 'datePicker',
      variant: 'input',
      required: true,
      wrapStyle: cnj(classes.formItem),
      label: t('issue_date'),
      maxDate: new Date(),
    },
    {
      name: 'link',
      helperText: t('website_helper'),
      cp: 'input',
      label: t('link'),
      wrapStyle: cnj(classes.formItem),
    },

    {
      name: 'description',
      visibleOptionalLabel: true,
      cp: 'richtext',
      label: t('description'),
      wrapStyle: cnj(classes.formItem, classes.grow),
      maxLength: DESCRIPTION_MAX_LENGTH,
      className: classes.growingDescription,
      showEmoji: false,
    },
  ].reduce((prev: any, curr) => {
    if (curr.name === 'description' && isHighlight) {
      return prev;
    }
    return [...prev, curr];
  }, []);
}
export const getHonorValidationSchema = () =>
  formValidator.object().shape({
    link: linkValidation,
    description: descriptionLengthValidator,
  });

export const transformHonor = ({
  institution,
  title,
  description,
  ...rest
}: Award) => ({
  ...rest,
  awardTitle: title?.label,
  awardLookupId: title?.value,
  institutionName: institution?.label,
  institutionPageId: `${institution?.value}`?.includes?.('_temp')
    ? null
    : institution?.value,
  pageCroppedImageUrl: institution?.image,
  description: removeBreaksAndSpaces(description),
});
