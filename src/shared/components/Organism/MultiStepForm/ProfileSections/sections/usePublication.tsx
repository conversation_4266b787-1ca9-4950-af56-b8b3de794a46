import React from 'react';
import Endpoints from 'shared/utils/constants/endpoints';
import useTranslation from 'shared/utils/hooks/useTranslation';
import formValidator from 'shared/utils/form/formValidator';

import hereApiResponseNormalizer from 'shared/utils/normalizers/hereApiResponseNormalizer';
import linkValidation from 'shared/utils/form/formValidator/customValidations/linkValidation';
import type Publication from 'shared/types/publication';
import removeBreaksAndSpaces from 'shared/utils/toolkit/removeBreaksAndSpaces';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import cnj from 'shared/uikit/utils/cnj';
import { DESCRIPTION_MAX_LENGTH } from 'shared/constants/enums';
import Flex from 'shared/uikit/Flex';
import { publicationNormalizer } from 'shared/utils/userAccomplishment.utils';
import HIGHLIGHT_TYPES from 'shared/constants/highlightTypes';
import descriptionLengthValidator from 'shared/constants/descriptionLengthValidator';
import useAuthUser from '@shared/utils/hooks/useAuthUser';
import { type SingleDataItem } from '@shared/components/Organism/MultiStepForm/ProfileSections/types';
import { ViewCard } from '../Components/ViewCard';
import { CombinedFooter } from '../Components/CombinedFooter';
import { getHeaderProps } from './utils/getHeaderProps';
import { useUtils } from './utils/useUtils';
import { profileSectionsStepKeys } from '../constants';
import classes from './styles.module.scss';

const usePublication = (): SingleDataItem => {
  const { t } = useTranslation();
  const { data: authUser } = useAuthUser();
  const countryCode = authUser?.location?.countryCode;

  const {
    data,
    deleteWithConfirm,
    onSuccess,
    handleClick,
    handleBackWithConfirm,
    isSingle,
    deleteItem,
    activeState,
    setActiveState,
    activeItem,
    editUrl,
  } = useUtils({
    url: Endpoints.App.User.Publication.get,
    highlightDataTransformer: (data) => ({
      ...data,
      profileEntityId: data.id,
      subTitle: data.publisherName,
      title: data.title,
      type: HIGHLIGHT_TYPES.PUBLICATION,
    }),
  });
  const publications = data?.publications?.map(publicationNormalizer);

  const initialValues = {
    ...(activeItem || {}),
  };

  return {
    stepKey: profileSectionsStepKeys.PUBLICATION,
    url: activeState === 'edit' ? editUrl : Endpoints.App.User.Publication.get,
    method: activeState === 'edit' ? 'PUT' : 'POST',
    onSuccess,
    initialValues,
    getValidationSchema: getPublicationValidationSchema,
    transform: transformPublication,
    getHeaderProps: ({ setStep, dirty }) =>
      getHeaderProps({
        dirty,
        setStep,
        activeState,
        handleBackWithConfirm: handleBackWithConfirm(
          profileSectionsStepKeys.ACCOMPLISHMENT
        ),
        titles: {
          main: t('publications'),
          add: t('add_publication'),
          edit: t('edit_publication'),
        },
        isSingle,
      }),
    renderFooter: ({ setStep, dirty }) => (
      <CombinedFooter
        setStep={setStep}
        activeState={activeState}
        deleteWithConfirm={deleteWithConfirm(deleteItem)}
        dirty={dirty}
        handleBackWithConfirm={handleBackWithConfirm(
          profileSectionsStepKeys.ACCOMPLISHMENT
        )}
        setActiveState={setActiveState}
        data={publications}
        addLabel={t('add_publication')}
      />
    ),
    renderBody: ({ values }) => {
      if (activeState === 'list' && publications?.length) {
        return (
          <Flex className={classes.listContainer}>
            {publications?.map((item: any) => (
              <ViewCard
                item={{
                  id: item?.id,
                  image: item?.image,
                  firstText: item?.realData?.title,
                  secondText: item?.realData?.publisher?.label,
                  thirdText: item?.secondText,
                  onClick: handleClick(publications),
                }}
              />
            ))}
          </Flex>
        );
      }
      return (
        <DynamicFormBuilder
          className={classes.formRoot}
          groups={getPublicationGroups({ t, countryCode })}
        />
      );
    },
  };
};

export default usePublication;

export function getPublicationGroups({
  t,
  isHighlight = false,
  countryCode,
}: any) {
  return [
    {
      name: 'title',
      cp: 'input',
      maxLength: 100,
      label: t('title'),
      helperText: t('publication_title_helper'),
      required: true,
      wrapStyle: cnj(classes.formItem),
    },
    {
      name: 'publisher',
      cp: 'avatarAsyncAutoComplete',
      isCompany: true,
      maxLength: 100,
      label: t('publisher'),
      required: true,
      url: `${Endpoints.App.Common.suggestPlace}`,
      normalizer: hereApiResponseNormalizer,
      wrapStyle: cnj(classes.formItem),
      params: {
        countryCode,
      },
      visibleRightIcon: true,
      rightIconProps: { name: 'search' },
    },
    {
      name: 'date',
      cp: 'datePicker',
      variant: 'input',
      wrapStyle: cnj(classes.formItem),
      required: true,
      label: t('issue_date'),
      maxDate: new Date(),
    },
    {
      name: 'link',
      cp: 'input',
      label: t('link'),
      wrapStyle: cnj(classes.formItem),
      helperText: t('website_helper'),
    },

    {
      name: 'description',
      visibleOptionalLabel: true,
      cp: 'richtext',
      label: t('description'),
      wrapStyle: cnj(classes.formItem, classes.grow),
      maxLength: DESCRIPTION_MAX_LENGTH,
      className: classes.growingDescription,
      showEmoji: false,
    },
  ].reduce((prev: any, curr) => {
    if (curr.name === 'description' && isHighlight) {
      return prev;
    }
    return [...prev, curr];
  }, []);
}

export const getPublicationValidationSchema = () =>
  formValidator.object().shape({
    link: linkValidation,
    description: descriptionLengthValidator,
  });

export const transformPublication = ({
  publisher,
  description,
  ...rest
}: Publication) => ({
  ...rest,
  publisherName: publisher?.label,
  publisherPageId: `${publisher?.value}`?.includes?.('_temp')
    ? null
    : publisher?.value,
  pageCroppedImageUrl: publisher?.image,
  description: removeBreaksAndSpaces(description),
});
