import React from 'react';
import Endpoints from 'shared/utils/constants/endpoints';
import useTranslation from 'shared/utils/hooks/useTranslation';
import formValidator from 'shared/utils/form/formValidator';
import lookupResponseNormalizer from 'shared/utils/normalizers/lookupResponseNormalizer';
import type CourseType from 'shared/utils/CourseType';
import removeBreaksAndSpaces from 'shared/utils/toolkit/removeBreaksAndSpaces';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import cnj from 'shared/uikit/utils/cnj';
import hereApiResponseNormalizer from 'shared/utils/normalizers/hereApiResponseNormalizer';
import { DESCRIPTION_MAX_LENGTH } from 'shared/constants/enums';
import Flex from 'shared/uikit/Flex';
import { courseNormalizer } from 'shared/utils/course.utils';
import useMedia from 'shared/uikit/utils/useMedia';
import HIGHLIGHT_TYPES from 'shared/constants/highlightTypes';
import useAuthUser from '@shared/utils/hooks/useAuthUser';
import descriptionLengthValidator from 'shared/constants/descriptionLengthValidator';
import { type SingleDataItem } from '@shared/components/Organism/MultiStepForm/ProfileSections/types';
import { useUtils } from './utils/useUtils';
import { getHeaderProps } from './utils/getHeaderProps';
import { CombinedFooter } from '../Components/CombinedFooter';
import { ViewCard } from '../Components/ViewCard';
import { profileSectionsStepKeys } from '../constants';
import classes from './styles.module.scss';

const useCourse = (): SingleDataItem => {
  const { t } = useTranslation();

  const { isMoreThanTablet } = useMedia();
  const { data: authUser } = useAuthUser();
  const countryCode = authUser?.location?.countryCode;

  const {
    data,
    deleteWithConfirm,
    onSuccess,
    handleClick,
    handleBackWithConfirm,
    isSingle,
    deleteItem,
    activeState,
    setActiveState,
    activeItem,
    editUrl,
  } = useUtils({
    url: Endpoints.App.User.Course.get,
    highlightDataTransformer: (data) => ({
      ...data,
      profileEntityId: data.id,
      subTitle: data.institutionName,
      title: data.title,
      type: HIGHLIGHT_TYPES.COURSE,
    }),
  });
  const courses = data?.courses?.map(courseNormalizer);

  const initialValues = {
    ...(activeItem || {}),
  };

  return {
    stepKey: profileSectionsStepKeys.COURSE,
    url: activeState === 'edit' ? editUrl : Endpoints.App.User.Course.get,
    method: activeState === 'edit' ? 'PUT' : 'POST',
    onSuccess,
    initialValues,
    getValidationSchema: getCourseValidationSchema,
    transform: transformCourse,
    getHeaderProps: ({ setStep, dirty }) =>
      getHeaderProps({
        dirty,
        setStep,
        activeState,
        handleBackWithConfirm: handleBackWithConfirm(
          profileSectionsStepKeys.EDUCATION
        ),
        titles: {
          main: t('courses'),
          add: t('add_course'),
          edit: t('edit_course'),
        },
        isSingle,
      }),
    renderFooter: ({ setStep, dirty }) => (
      <CombinedFooter
        setStep={setStep}
        activeState={activeState}
        deleteWithConfirm={deleteWithConfirm(deleteItem)}
        dirty={dirty}
        handleBackWithConfirm={handleBackWithConfirm(
          profileSectionsStepKeys.EDUCATION
        )}
        setActiveState={setActiveState}
        data={courses}
        addLabel={t('add_course')}
      />
    ),
    renderBody: ({ values }) => {
      if (activeState === 'list' && courses?.length) {
        return (
          <Flex className={classes.listContainer}>
            {courses?.map((item: any) => (
              <ViewCard
                item={{
                  id: item?.id,
                  image: item?.image,
                  firstText: item?.realData?.title?.label,
                  secondText: item?.realData?.institution?.label,
                  thirdText: item?.secondText,
                  onClick: handleClick(courses),
                }}
              />
            ))}
          </Flex>
        );
      }
      return (
        <DynamicFormBuilder
          className={classes.formRoot}
          groups={getCourseGroups({
            t,
            values,
            isMoreThanTablet,
            countryCode,
          })}
        />
      );
    },
  };
};

export default useCourse;

export function getCourseGroups({
  t,
  isMoreThanTablet,
  values,
  isHighlight = false,
  isEdit = false,
  countryCode,
}: any) {
  return [
    {
      name: 'title',
      cp: 'asyncAutoComplete',
      maxLength: 100,
      label: t('course'),
      wrapStyle: cnj(classes.formItem),
      url: Endpoints.App.Common.getOccupations,
      normalizer: lookupResponseNormalizer,
      required: true,
      visibleRightIcon: true,
      rightIconProps: { name: 'search' },
    },
    {
      name: 'institution',
      cp: 'avatarAsyncAutoComplete',
      isCompany: true,
      maxLength: 100,
      label: t('institution'),
      visibleRightIcon: true,
      rightIconProps: { name: 'search' },
      required: true,
      normalizer: hereApiResponseNormalizer,
      url: `${Endpoints.App.Common.suggestPlace}`,
      params: {
        countryCode,
      },
      wrapStyle: cnj(classes.formItem),
    },
    {
      name: 'startDate',
      cp: 'datePicker',
      variant: 'input',
      wrapStyle: cnj(classes.formItem, isMoreThanTablet && classes.grow),
      required: true,
      picker: 'month',
      label: t('start_date'),
      isFirstHalfWidth: isMoreThanTablet && true,
      rowContainerClassName: classes.rowContainerClassName,
      maxDate: new Date(),
      halfWidthWhenOpen: true,
    },
    {
      name: 'endDate',
      cp: 'datePicker',
      wrapStyle: cnj(classes.formItem, isMoreThanTablet && classes.grow),
      variant: 'input',
      picker: 'month',
      visibleOptionalLabel: false,
      minDate: values?.startDate ? new Date(values?.startDate) : undefined,
      label: t('end_date'),
      isSecondHalfWidth: isMoreThanTablet && true,
      halfWidthWhenOpen: true,
    },
    {
      name: 'description',
      cp: 'richtext',
      label: t('description'),
      wrapStyle: cnj(classes.formItem, classes.grow),
      maxLength: DESCRIPTION_MAX_LENGTH,
      visibleOptionalLabel: true,
      className: classes.growingDescription,
      showEmoji: false,
    },
  ].reduce((prev: any, curr) => {
    if (isHighlight && curr.name === 'description') {
      return prev;
    }
    if (curr.name === 'share' && isEdit) {
      return prev;
    }
    return [...prev, curr];
  }, []);
}

export function getCourseValidationSchema() {
  return formValidator.object().shape({
    endDate: formValidator
      .date()
      .typeError('this_field_is_required')
      .when(
        ['startDate', 'currentlyWorking'],
        (startDate, currentlyWorking, schema) => {
          if (currentlyWorking) {
            return schema.nullable();
          }
          return startDate
            ? schema
                .min(startDate, 'date_e_b_s')
                .required('this_field_is_required')
            : schema.nullable();
        }
      ),
    description: descriptionLengthValidator,
  });
}

export const transformCourse = ({
  title,
  institution,
  description,
  ...rest
}: CourseType) => ({
  ...rest,
  title: title?.label,
  titleLookupId: title?.value,
  institutionName: institution?.label,
  institutionPageId: `${institution?.value}`?.includes?.('_temp')
    ? null
    : institution?.value,
  pageCroppedImageUrl: institution?.image,
  description: removeBreaksAndSpaces(description),
});
