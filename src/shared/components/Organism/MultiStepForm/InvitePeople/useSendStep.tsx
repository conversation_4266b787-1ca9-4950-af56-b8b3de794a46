import { PAGE_ROLES } from 'shared/utils/constants/enums';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useAuthUser from 'shared/utils/hooks/useAuthUser';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import type { Dispatch, SetStateAction } from 'react';
import GoogleIcon from 'shared/uikit/shared/svg/GoogleIcon';
import InfoCard from 'shared/uikit/InfoCard';
import { RichTextView } from 'shared/uikit/RichText';
import RadioCardGroup from 'shared/uikit/RadioCardGroup';
import AvatarCard from 'shared/uikit/AvatarCard';
import LogoCopyRight from 'shared/uikit/LogoCopyRight';
import EmailIcon2 from 'shared/svg/landing/EmailIcon2';
import type { DataReturnType } from 'shared/hooks/api-hook/useGetMyPages';
import useGetMyPages from 'shared/hooks/api-hook/useGetMyPages';
import { getPageRoleIndex } from 'shared/utils/getPageRoleIndex';
import { pageStatus } from 'shared/constants/enums';
import type { Method } from '@shared/components/Organism/MultiStepForm/InvitePeople/types';
import { TwoButtonFooter } from '../ProfileSections/Components/TwoButtonFooter';
import type { MultiStepFormProps } from '../MultiStepForm';
import { StepHeader } from './StepHeader';
import classes from './useSendStep.module.scss';

type SingleDataItem = {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
  renderFooter: Exclude<MultiStepFormProps['renderFooter'], undefined>;
};

type Args = {
  method: Method;
  sendMethod: 'user' | 'anonymous' | (string & {});
  setSendMethod: Dispatch<SetStateAction<'user' | 'anonymous' | (string & {})>>;
};

export function useSendStep({
  method,
  sendMethod,
  setSendMethod,
}: Args): SingleDataItem[] {
  const { t } = useTranslation();
  const { data: authUser } = useAuthUser();

  const renderFooter: SingleDataItem['renderFooter'] = () => (
    <TwoButtonFooter submitLabel={t('invite')} />
  );

  const getHeaderProps: SingleDataItem['getHeaderProps'] = ({
    setStep,
    values,
  }) => ({
    title: method === 'google' ? t('google_invite') : t('email_invite'),
    hideBack: false,
    noCloseButton: true,
    backButtonProps: {
      onClick: () => setStep((prev) => prev - 1),
    },
    belowContent:
      method === 'google' ? (
        <StepHeader
          title={values?.selectedAccount || ''}
          subtitle={t('google')}
          icon={<GoogleIcon />}
        />
      ) : (
        <StepHeader title={t('email')} icon={<EmailIcon2 />} />
      ),
  });

  const { data: myPages = [] }: any = useGetMyPages();

  const pages = [
    ...myPages.reduce(
      (prev, curr: DataReturnType) =>
        curr?.page?.status === pageStatus.PUBLISHED
          ? [
              {
                ...curr,
                pageRoleIndex: curr.roles.reduce(
                  (prev, curr) => prev + getPageRoleIndex(curr),
                  0
                ),
                roles: curr.roles.map((role) => t(PAGE_ROLES[role].label)),
              },
              ...prev,
            ]
          : prev,
      []
    ),
  ]?.sort((a, b) => (a.pageRoleIndex < b.pageRoleIndex ? -1 : 1));

  const data: Array<SingleDataItem> = [
    {
      stepKey: 'methods',
      getHeaderProps,
      renderBody: ({ setStep, values }) => (
        <Flex className={classes.wrapper}>
          <Typography
            font="700"
            color="smoke_coal"
            size={20}
            height={28}
            mb={4}
          >
            {t('send_as')}
          </Typography>
          <Typography
            color="secondaryDisabledText"
            size={15}
            height={21}
            mb={16}
          >
            {t('you_can_send_invitation_as_yourself_or_lobox')}
          </Typography>

          <RadioCardGroup
            options={[
              {
                renderItem: () => (
                  <AvatarCard
                    noHover
                    data={{
                      title: authUser?.fullName,
                      subTitle: `@${authUser?.username}`,
                      image: authUser?.croppedImageUrl,
                    }}
                    key={authUser?.id}
                    avatarProps={{
                      isCompany: false,
                    }}
                    containerProps={{
                      className: classes.noPadding,
                    }}
                  />
                ),
                value: 'user',
              },
              { isDivider: true },
              pages?.map(({ page, id }) => ({
                value: id,
                renderItem: () => (
                  <AvatarCard
                    noHover
                    data={{
                      title: page?.title,
                      subTitle: `@${page?.user}`,
                      image: page?.croppedImageUrl,
                    }}
                    key={authUser?.id}
                    avatarProps={{
                      isCompany: true,
                    }}
                    containerProps={{
                      className: classes.noPadding,
                    }}
                  />
                ),
              })),
              { isDivider: true },
              {
                renderItem: () => <LogoCopyRight className={classes.logo} />,
                value: 'anonymous',
              },
            ]?.flat()}
            value={sendMethod}
            onChange={(value: any) => setSendMethod(value)}
          />

          <Typography
            font="700"
            color="smoke_coal"
            size={20}
            height={28}
            mb={4}
            mt={20}
          >
            {t('invite_message')}
          </Typography>
          <Typography
            color="secondaryDisabledText"
            size={15}
            height={21}
            mb={16}
          >
            {t('your_contact_will_see_this_message')}
          </Typography>
          <Flex className={classes.messageWrapper}>
            <Typography
              color="secondaryDisabledText"
              size={12}
              height={14}
              mb={5}
            >
              {t('message')}
            </Typography>
            <RichTextView
              html={t(
                sendMethod === 'anonymous'
                  ? 'anonymous_message_to_invite_people'
                  : 'user_sent_message_to_invite_people'
              )}
              typographyProps={{
                size: 15,
                font: '400',
                color: 'smoke_coal',
                height: 21,
                isWordWrap: true,
              }}
              row={1}
              showMore={false}
            />
          </Flex>
          <Flex className={classes.grow} />
          {method === 'bulk' && (
            <InfoCard
              classNames={{ wrapper: classes.infoWrapper }}
              label={t('it_may_take_few_hours_to_emaill_all_invitations')}
              leftIconProps={{
                size: 20,
              }}
            />
          )}
        </Flex>
      ),
      renderFooter,
    },
  ];

  return data;
}
