import useTranslation from 'shared/utils/hooks/useTranslation';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import { HorizontalDividerWithLabel } from 'shared/components/molecules/HorizontalDividerWithLabel';
import type { Dispatch, SetStateAction } from 'react';
import { useEffect, useRef, useState } from 'react';
import InputTags from 'shared/uikit/InputTags';
import InfoCard from 'shared/uikit/InfoCard';
import EmailIcon2 from 'shared/svg/landing/EmailIcon2';
import type { Method } from '@shared/components/Organism/MultiStepForm/InvitePeople/types';
import { StepHeader } from './StepHeader';
import { UploadBulkEmail } from './UploadBulkEmail';
import type { MultiStepFormProps } from '../MultiStepForm';
import { TwoButtonFooter } from '../ProfileSections/Components/TwoButtonFooter';
import classes from './useEmail.module.scss';

type SingleDataItem = {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
  renderFooter?: Exclude<MultiStepFormProps['renderFooter'], undefined>;
};

type Args = {
  setMethod: Dispatch<SetStateAction<Method>>;
};

export type BulkData = { count: number; name: string };

export function useEmail({ setMethod }: Args): SingleDataItem[] {
  const [emailInputKey, setEmailInputKey] = useState(new Date().toISOString());
  const { t } = useTranslation();
  const [emails, setEmails] = useState<string[]>([]);
  const [bulkData, setBulkData] = useState<undefined | BulkData>();
  const tagInputRef = useRef<{ handleClear: Function }>();

  useEffect(() => {
    if (!bulkData) return;
    setMethod('bulk');
    setEmails([]);
    tagInputRef.current?.handleClear?.();
    setEmailInputKey(new Date().toISOString());
  }, [bulkData]);

  const getHeaderProps: SingleDataItem['getHeaderProps'] = ({ setStep }) => ({
    title: t('email_invite'),
    hideBack: false,
    noCloseButton: true,
    backButtonProps: {
      onClick: () => setStep((prev) => prev - 1),
    },
    belowContent: <StepHeader title={t('email')} icon={<EmailIcon2 />} />,
  });

  const renderFooter: SingleDataItem['renderFooter'] = ({ setStep, step }) => {
    const goNext = () => setStep((prev) => prev + 1);
    const disabled = !bulkData?.count && !emails?.length;
    return (
      <TwoButtonFooter
        submitLabel={t('next')}
        onSubmitClick={goNext}
        disabledSubmit={disabled}
      />
    );
  };

  const data: Array<SingleDataItem> = [
    {
      stepKey: 'google-accounts-list',
      getHeaderProps,
      renderBody: ({ setStep, values, setFieldValue }) => (
        <Flex className={classes.bodyWrapper}>
          <Typography size={15} height={21} color="secondaryDisabledText">
            {t('enjoy_our_built_in_invites_or_bulk_emails_to_invute')}
          </Typography>
          <InputTags
            key={emailInputKey}
            onChange={(mails) => {
              setEmails(mails);
              setFieldValue?.('emails', mails);
            }}
            label={t('add_email')}
            errorText={t('enter_valid_email')}
            maxTags={10}
            showMaxTagsHelper
            disabled={!!bulkData}
            ref={tagInputRef}
          />
          <HorizontalDividerWithLabel
            label={t('or_send_bulk_emails')}
            classNames={{ wrapper: classes.noMargin }}
          />
          <UploadBulkEmail data={bulkData} setData={setBulkData} />
          <InfoCard
            classNames={{
              wrapper: classes.infoCard,
              label: classes.infoCardLabel,
            }}
            label={t('you_can_proceed_with_only_one_method')}
            leftIconProps={{
              name: 'info-circle',
              color: 'secondaryDisabledText',
              size: 20,
            }}
          />
        </Flex>
      ),
      renderFooter,
    },
  ];

  return data;
}
