import React, { useEffect, useState, type FC } from 'react';
import dynamic from 'next/dynamic';
import type { IResume } from '@shared/types/job';
import { useFormikContext } from 'formik';
import Skeleton from '@shared/uikit/Skeleton';
import type { JobApplicationFormData } from '@shared/components/Organism/MultiStepForm/JobApplication/types';
import type { CallbackParams } from '../../MultiStepForm';
import JobApplicationQuestions from './JobApplicationBody/JobApplicationQuestions';

const JobApplicationStepOne = dynamic(
  () => import('./JobApplicationBody/JobApplicationStepOne'),
  { ssr: false, loading: () => <Skeleton className="h-[500px]" /> }
);
const JobApplicationStepTwo = dynamic(
  () => import('./JobApplicationBody/JobApplicationStepTwo'),
  { ssr: false }
);
const JobApplicationStepPreview = dynamic(
  () => import('./JobApplicationBody/JobApplicationStepPreview'),
  { ssr: false }
);

const JobApplicationBody: FC<
  CallbackParams & { resumeData?: IResume; stepKey?: string }
> = (props) => {
  const { resumeData, stepKey } = props;
  const [isPasted, setIsPasted] = useState(false);

  const { values, setFieldValue } = useFormikContext() as {
    values: JobApplicationFormData;
    setFieldValue: Function;
  };

  useEffect(() => {
    if (resumeData && !values.resume) setFieldValue('resume', resumeData);
  }, []);

  const layout = () => {
    switch (stepKey) {
      case 'personal_info': {
        return (
          <JobApplicationStepOne
            isPasted={isPasted}
            setIsPasted={() => setIsPasted(true)}
          />
        );
      }
      case 'resume': {
        return <JobApplicationStepTwo />;
      }
      case 'questions': {
        return <JobApplicationQuestions />;
      }
      case 'preview': {
        return <JobApplicationStepPreview {...props} />;
      }
      default: {
        return <p>Success step</p>;
      }
    }
  };

  return <>{layout()}</>;
};

export default JobApplicationBody;
