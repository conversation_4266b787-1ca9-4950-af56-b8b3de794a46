import type { SectionTitleProps } from '@shared/components/Organism/Objects/Common/SectionTitle';
import SectionTitle from '@shared/components/Organism/Objects/Common/SectionTitle';
import SearchCard from '@shared/components/Organism/SearchCard';
import useGetAppObject from '@shared/hooks/useGetAppObject';
import IconButton from '@shared/uikit/Button/IconButton';
import Flex from '@shared/uikit/Flex';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { useFormikContext } from 'formik';
import * as React from 'react';
import Typography from '@shared/uikit/Typography';
import cnj from '@shared/uikit/utils/cnj';
import { UploadedResumeCard } from '@shared/components/Organism/UploadedResumeSection/UploadedResumeCard.component';
import RichText from '@shared/uikit/RichText';
import { useMultiStepFormState } from '@shared/hooks/useMultiStepForm';
import DividerVertical from '@shared/uikit/Divider/DividerVertical';
import type { JobApplicationFormData } from '@shared/components/Organism/MultiStepForm/JobApplication/types';
import type { CallbackParams } from '../../../MultiStepForm';

const JobApplicationStepPreview: React.FunctionComponent<CallbackParams> = (
  props
) => {
  const { setStep } = props;
  const { t } = useTranslation();
  const { values } = useFormikContext() as { values: JobApplicationFormData };
  const { authUser } = useGetAppObject();
  const { data: job } = useMultiStepFormState('jobApplication');

  const hasCL = values.coverLetter?.length;
  const hasPhone = !!values.phone?.length;
  const hasResume = !!Object.keys(values.resume ?? {})?.length;

  return (
    <Flex className="gap-32">
      <SearchCard
        imgSrc={authUser?.croppedImageUrl as string}
        firstText={authUser?.fullName as string}
        secondText={authUser?.usernameAtSign as string}
        thirdText={authUser?.occupation?.label as string}
        fourthText={authUser?.location?.title as string}
        isHoverAble={false}
        classNames={{ container: '!p-0' }}
      />
      <Flex className="gap-12">
        <ApplicationPreviewTitle
          title={t('personal_info_and_resume')}
          onClick={() => setStep(0)}
        />
        <ApplicationPreviewItem title={t('email')}>
          {values.email}
        </ApplicationPreviewItem>
        <ApplicationPreviewItem title={t('phone_number')} isEmpty={!hasPhone}>
          {hasPhone ? values.phone : t('no_phone_entered')}
        </ApplicationPreviewItem>
        <ApplicationPreviewItem title={t('cover_letter')} isEmpty={!hasCL}>
          {hasCL ? (
            <RichText
              value={values.coverLetter}
              readOnly
              showEmoji={false}
              className="!max-h-none !border-none !p-0"
            />
          ) : (
            t('no_cover_letter_entered')
          )}
        </ApplicationPreviewItem>
        <ApplicationPreviewItem title={t('resume')} isEmpty={!hasResume}>
          {hasResume ? (
            <UploadedResumeCard
              name={authUser?.name}
              classNames={{
                wrapper:
                  'mt-4 p-16 border border-solid border-techGray_20 rounded-xl',
                title: 'flex-1 !max-w-none !mb-0 !ml-8',
                buttons: '!w-auto !ml-none',
              }}
              showControls
              mobileView
            />
          ) : (
            t('no_resume_entered')
          )}
        </ApplicationPreviewItem>
      </Flex>
      {!!job?.questions.length && (
        <Flex className="gap-12">
          <ApplicationPreviewTitle
            title={t('questions')}
            onClick={() => setStep(2)}
          />
          <Flex className="!flex-row">
            <ApplicationPreviewItem
              title={t('total_questions')}
              className="w-full"
            >
              {job.questions.length.toString()}
            </ApplicationPreviewItem>
            <DividerVertical distance={12} />
            <ApplicationPreviewItem
              title={t('questions_answered')}
              className="w-full"
            >
              {values.answers?.length.toString()}
            </ApplicationPreviewItem>
          </Flex>
        </Flex>
      )}
    </Flex>
  );
};

export default JobApplicationStepPreview;

const ApplicationPreviewTitle = (
  props: SectionTitleProps & { onClick: VoidFunction }
) => {
  const { title, onClick, ...rest } = props;
  return (
    <SectionTitle
      {...rest}
      title={title}
      actionButton={
        <IconButton name="pen-light" type="far" onClick={onClick} />
      }
      wrapperStyle="!m-0"
      titleProps={{
        size: 16,
        height: 22,
        font: '500',
        color: 'smoke_coal',
      }}
    />
  );
};

interface ApplicationPreviewItemProps {
  title: string;
  className?: string;
  children: string | React.ReactNode;
  isEmpty?: boolean;
}
const ApplicationPreviewItem = (props: ApplicationPreviewItemProps) => {
  const { title, children, className, isEmpty } = props;

  return (
    <Flex className={cnj('gap-4', className)}>
      <Typography size={13} height={15} font="500" color="colorIconForth2">
        {title}
      </Typography>
      {typeof children === 'string' ? (
        <Typography
          color={isEmpty ? 'secondaryDisabledText' : 'smoke_coal'}
          size={14}
          height={18}
          font="400"
        >
          {children}
        </Typography>
      ) : (
        children
      )}
    </Flex>
  );
};
