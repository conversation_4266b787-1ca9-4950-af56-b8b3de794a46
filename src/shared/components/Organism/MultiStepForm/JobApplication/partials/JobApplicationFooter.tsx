import eventKeys from '@shared/constants/event-keys';
import type { MultiStepFormFooterProps } from '@shared/types/generalProps';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import useTranslation from '@shared/utils/hooks/useTranslation';
import event from '@shared/utils/toolkit/event';
import { useFormikContext } from 'formik';
import type { FC } from 'react';
import SubmitButton from '@shared/uikit/Form/SubmitButton';
import ValidationFormButton from '@shared/components/atoms/ValidationFormButton';
import Observer from '@shared/components/atoms/Observer';
import type { JobApplicationFormData } from '@shared/components/Organism/MultiStepForm/JobApplication/types';

const JobApplicationFooter: FC<
  MultiStepFormFooterProps & { stepKey?: string }
> = (props) => {
  const { step, setStep, validateForm, isSubmitting, stepKey, isSubmitStep } =
    props;
  const { t } = useTranslation();
  const formikContext = useFormikContext() as {
    values: JobApplicationFormData;
    setFieldValue: Function;
  };
  const { values, setFieldValue } = formikContext;
  const handleClickDiscard = () => {
    event.trigger(eventKeys.closeModal);
    return null;
  };
  const handleClickSave = async () => {
    if (stepKey === 'questions') {
      const answers = mapAnswers(values);
      setFieldValue('answers', answers);
    }
    setStep((prev) => prev + 1);
  };

  return (
    <Flex className="!flex-row gap-8">
      <Observer step={step} validateForm={validateForm} />
      <Button
        fullWidth
        label={t('discard')}
        schema="gray-semi-transparent"
        onClick={handleClickDiscard}
      />
      {isSubmitStep ? (
        <SubmitButton fullWidth label={t('apply')} schema="primary-blue" />
      ) : (
        <ValidationFormButton onValidate={handleClickSave} />
      )}
    </Flex>
  );
};

export default JobApplicationFooter;

const mapAnswers = (values: JobApplicationFormData) =>
  Object.keys(values)
    .filter((key) => key.startsWith('answer_'))
    .map((key) => {
      const questionId = parseInt(key.split('_')[1], 10);
      return { questionId, answer: (values as any)[key] as string };
    });
