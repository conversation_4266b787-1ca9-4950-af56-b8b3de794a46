import type { FC } from 'react';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  closeMultiStepForm,
  useMultiStepFormState,
} from 'shared/hooks/useMultiStepForm';
import type { MultiStepFormProps } from 'shared/components/Organism/MultiStepForm/MultiStepForm';
import MultiStepForm from 'shared/components/Organism/MultiStepForm/MultiStepForm';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import getStepData from '@shared/utils/getStepData';
import formValidator, {
  phoneNumberValidation,
} from '@shared/utils/form/formValidator';
import jobsApi from 'shared/utils/api/jobs';
import useJobElement from '@app/jobs/partials/hooks/useJobElement';
import useUpdateQueryData from '@shared/utils/hooks/useUpdateQueryData';
import type { QueryKeyType } from '@shared/types/general';
import useToast from '@shared/uikit/Toast/useToast';
import useTranslation from '@shared/utils/hooks/useTranslation';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import type { JobApplicationFormData } from '@shared/components/Organism/MultiStepForm/JobApplication/types';
import useJobApplicationMultiStepForm from './useJobApplicationMultiStepForm';
import JobApplicationSuccessModal from './partials/JobApplicationSuccessModal';

const JobApplicationMultiStepForm: FC = () => {
  const { authUser } = useGetAppObject();
  const data = useJobApplicationMultiStepForm();
  const { data: job } = useMultiStepFormState('jobApplication');
  const toast = useToast();
  const { t } = useTranslation();
  const { updateJobData, queryKey, jobElement } = useJobElement();
  const { handleChangeParams } = useCustomParams();
  const { replace } = useUpdateQueryData(queryKey as QueryKeyType);
  const [showSuccess, setShowSuccess] = useState<{ resume?: File } | undefined>(
    undefined
  );

  const totalSteps = useMemo(() => data.length ?? 0, [data]);
  const getHeaderProps = getStepData('getHeaderProps', data);
  const getStepHeaderProps = getStepData('getStepHeaderProps', data);
  const renderFooter = getStepData('renderFooter', data);
  const renderBody = getStepData('renderBody', data);

  const defaultValues: JobApplicationFormData = {
    jobId: job?.id,
    phone: authUser?.phone?.value,
    email: authUser?.email?.value,
    coverLetter: '',
    resumeUrl: '',
    answers: [],
  };

  const getValidationSchema: MultiStepFormProps['getValidationSchema'] =
    useCallback(({ step }: { step: number }) => {
      const validationSchema = {
        0: {
          phone: phoneNumberValidation,
        },
      } as any;
      return formValidator.object().shape(validationSchema[step]);
    }, []);

  const onSuccess = (
    setStep: Function,
    res: any,
    values: JobApplicationAPIData
  ) => {
    const updatedDate = {
      applicationId: res?.id,
      applicantsCount: res?.applicantsCount || 0,
      isApplied: true,
      applied: true,
    };
    updateJobData(updatedDate);
    if (queryKey) {
      replace({
        ...jobElement,
        ...updatedDate,
      });
    }
    setShowSuccess({ resume: values.resumeFile });
  };

  const onClose = () => {
    closeMultiStepForm('jobApplication');
  };
  const onFailure = (error: any) => {
    toast({
      type: 'error',
      icon: 'times-circle',
      title: t('job_application'),
      message: error?.response?.data?.error ?? 'Error',
    });
    onClose();
  };

  const transform = (props: JobApplicationFormData) => {
    const answers = props.answers?.map((item) => {
      if ((item.answer as any)?.value)
        return {
          ...item,
          answer: [(item.answer as any).value],
        };
      if (typeof item.answer === 'string') {
        return {
          ...item,
          answer: [item.answer],
        };
      }
      return item;
    });
    const phone = (props.phone ?? '').length <= 5 ? '' : props.phone;
    return {
      jobId: props.jobId,
      email: props.email,
      phone,
      coverLetter: props.coverLetter,
      answers,
      resumeUrl: props.resumeUrl,
      resumeFile: props.resumeFile,
    };
  };

  const onCloseSuccess = () => {
    setShowSuccess(undefined);
    onClose();
  };

  useEffect(() => {
    handleChangeParams({ remove: ['formName'] });
  }, []);

  if (showSuccess)
    return (
      <JobApplicationSuccessModal
        onClose={onCloseSuccess}
        resume={showSuccess.resume}
      />
    );

  return (
    <MultiStepForm
      getValidationSchema={getValidationSchema}
      apiFunc={jobsApi.postApplication}
      totalSteps={totalSteps}
      initialValues={defaultValues}
      getHeaderProps={getHeaderProps}
      renderBody={renderBody}
      renderFooter={renderFooter}
      getStepHeaderProps={getStepHeaderProps}
      transform={transform}
      onClose={onClose}
      onSuccess={onSuccess}
      onFailure={onFailure}
      wide
      formName="jobApplication"
      isOpenAnimation
    />
  );
};

export default JobApplicationMultiStepForm;
