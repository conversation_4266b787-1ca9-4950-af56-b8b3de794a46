import type { IResume } from '@shared/types/job';

interface JobApplicationAPIData {
  jobId: string;
  phone?: string;
  email: string;
  coverLetter?: string;
  resumeUrl?: string;
  answers: { id: string; answer: string[] }[];
  resumeFile?: File;
}

export interface JobApplicationFormData
  extends Partial<Omit<JobApplicationAPIData, 'answers'>> {
  resume?: IResume;
  answers?: {
    questionId: string;
    answer: Array<string | { value: string; label: string }>;
  }[];
}
