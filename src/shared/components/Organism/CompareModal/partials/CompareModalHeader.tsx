import dayjs from 'dayjs';
import Flex from '@shared/uikit/Flex';
import Icon from '@shared/uikit/Icon';
import Typography from '@shared/uikit/Typography';
import IconButton from '@shared/uikit/Button/IconButton';
import useTranslation from '@shared/utils/hooks/useTranslation';

import { CompareVariant } from '..';

interface CompareModalHeaderProps {
  onClose: () => void;
  onBack: () => void;
  variant: CompareVariant;
}

const CompareModalHeader: React.FC<CompareModalHeaderProps> = ({
  onBack,
  onClose,
  variant,
}) => {
  const { t } = useTranslation();

  if (variant === 'readonly')
    return (
      <Flex className="!flex-row items-center gap-[14px] px-20 py-12 bg-popOverBg_white rounded-[12px]">
        <IconButton
          name="chevron-left"
          type="far"
          colorSchema="transparentSmokeCoal"
          onClick={onBack}
          size="md18"
        />

        <Typography size={24} font="700" height={28}>
          {t('compare_candidates')}
        </Typography>
      </Flex>
    );

  return (
    <Flex className="!flex-row items-center px-20 py-12 bg-popOverBg_white rounded-[12px]">
      <Typography size={24} font="700" height={28}>
        {t('compare_candidates')}
      </Typography>
      <Flex className="ml-auto !flex-row gap-4 items-center bg-gray_5 rounded-[4px] p-8">
        <Icon name="eye" type="far" size={16} color="secondaryDisabledText" />
        <Typography
          size={15}
          font="400"
          height={18}
          color="secondaryDisabledText"
        >
          {`${t('viewed_on')}:`}
        </Typography>
        <Typography size={15} font="400" height={18}>
          {dayjs().format('LL - LT')}
        </Typography>
      </Flex>
      <IconButton
        name="times"
        type="far"
        colorSchema="transparentSmokeCoal"
        onClick={onClose}
        size="md18"
        className="ml-8"
      />
    </Flex>
  );
};

export default CompareModalHeader;
