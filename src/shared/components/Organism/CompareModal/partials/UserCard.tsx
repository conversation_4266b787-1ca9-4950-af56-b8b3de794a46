import { type MouseEvent } from 'react';
import Flex from '@shared/uikit/Flex';
import Icon from '@shared/uikit/Icon';
import Typography from '@shared/uikit/Typography';
import Avatar from '@shared/uikit/Avatar';
import IconButton from '@shared/uikit/Button/IconButton';
import ObjectInfoCard from '@shared/components/molecules/ObjectInfoCard';
import useTranslation from '@shared/utils/hooks/useTranslation';
import cnj from '@shared/uikit/utils/cnj';
import classes from './index.module.scss';

import { CompareVariant } from '..';

interface UserCardProps {
  user?: any;
  setOpenCandidates?: (value?: boolean) => void;
  onRemove?: (id: string) => (event?: MouseEvent<any>) => void;
  variant: CompareVariant;
}

const UserCard = (props: UserCardProps) => {
  const { user, setOpenCandidates, onRemove } = props;
  const { t } = useTranslation();
  if (user)
    return (
      <Flex className={classes.cardWrapper}>
        <IconButton
          className={classes.iconWrapper}
          name="times"
          colorSchema="primary"
          onClick={onRemove?.(user.id)}
        />
        <Avatar
          imgSrc={user?.croppedImageUrl}
          bordered={false}
          isCompany={false}
          size="flg"
          className={classes.avatarWrapper}
        />
        <ObjectInfoCard
          firstText={`${user?.name} ${user.surname}`}
          secondText={`@${user?.username}`}
          thirdText={user?.occupation}
          fourthText={user?.location?.title}
          withAvatar={false}
          className="w-full"
        />
      </Flex>
    );
  if (props?.variant === 'readonly') return null;

  return (
    <Flex
      className={cnj(classes.cardWrapper, classes.emptyCard)}
      // className="justify-center items-center gap-10 p-16 w-full"
      onClick={() => setOpenCandidates?.(true)}
    >
      <Icon name="plus" type="far" size={28} color="secondaryDisabledText" />
      <Typography size={15} font="700" height={18} color="colorIconForth2">
        {t('add_candidate')}
      </Typography>
    </Flex>
  );
};

export default UserCard;
