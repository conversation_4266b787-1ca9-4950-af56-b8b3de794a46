import { type MouseEvent, useState } from 'react';
import FixedRightSideModalDialog from '@shared/uikit/Modal/FixedRightSideModalDialog';
import {
  useGlobalDispatch,
  useGlobalState,
} from '@shared/contexts/Global/global.provider';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import Flex from '@shared/uikit/Flex';
import Typography from '@shared/uikit/Typography';
import useTranslation from '@shared/utils/hooks/useTranslation';
import Grid from '@shared/uikit/Grid';
import type {
  JobAPIProps,
  LanguageLevelType,
  SkillLevelType,
} from '@shared/types/jobsProps';
import { QueryKeys } from '@shared/utils/constants';
import { compareUsers } from '@shared/utils/api/jobs';
import { useQuery } from '@tanstack/react-query';
import Skeleton from '@shared/uikit/Skeleton';
import type { NormalizedCompareData } from '@shared/utils/normalizers/compareNormalizer';
import ProgressItem from '@shared/uikit/ProgressItem';
import { experienceNormalizer } from '@shared/utils/experience.utils';
import { type BECandidateSearchResult } from '@shared/types/candidates';
import CompareModalHeader from './partials/CompareModalHeader';
import InfoCard from '../Objects/Common/InfoCard';
import AdvancedCardList from '../AdvancedCardList/AdvancedCardList.component';
import AboutSectionLayout from '../AboutSectionLayout/AboutSectionLayout.component';
import UserCard from './partials/UserCard';
import CompareItem from './partials/CompareItem';
import CandidatesListModal from './partials/CandidatesListModal';

interface CompareModalProps {
  data: JobAPIProps;
}

export type CompareVariant = 'editable' | 'readonly';

const CompareModal: React.FC<CompareModalProps> = () => {
  const globalDispatch = useGlobalDispatch();
  const { selectedUsers: globalSelectedUsers } = useGlobalState('compareModal');
  const { t } = useTranslation();
  const [selectedUsers, setSelectedUsers] = useState<BECandidateSearchResult[]>(
    globalSelectedUsers || []
  );
  const variant: CompareVariant = 'readonly';
  const onBack = () => {};

  console.log('selectedUsers', selectedUsers);

  const { data, isLoading } = useQuery<NormalizedCompareData[]>({
    queryKey: [QueryKeys.getCompareUsers, selectedUsers.map((user) => user.id)],
    queryFn: () =>
      compareUsers({
        users: selectedUsers,
      }),
  });
  const onClose = () => {
    globalDispatch({
      type: 'TOGGLE_COMPARE_MODAL',
      payload: { open: false },
    });
  };
  const [openCandidates, setOpenCandidates] = useState(false);
  const handleRemoveCandidate =
    (candidateId: string) => (event?: MouseEvent<any>) => {
      setSelectedUsers((prev) => prev.filter(({ id }) => id !== candidateId));
    };

  if (openCandidates)
    return (
      <CandidatesListModal
        isOpen={openCandidates}
        onClose={() => setOpenCandidates(false)}
        selectedUsers={selectedUsers}
        setSelectedUsers={setSelectedUsers}
        wide
        doubleColumn
        contentClassName="!max-w-full"
      />
    );

  return (
    <FixedRightSideModalDialog
      wide
      doubleColumn
      onBack={onClose}
      onClose={onClose}
      isOpenAnimation
      contentClassName="!max-w-full !bg-tooltipText overflow-hidden !border-l-0"
    >
      <ModalBody className="gap-20">
        <CompareModalHeader
          onClose={onClose}
          variant={variant}
          onBack={onBack}
        />
        <Flex className="!flex-row gap-12 bg-gray_5 rounded-[12px] p-20">
          <Grid container spacing={2} className="w-full">
            {selectedUsers.map((user) => (
              <Grid size={3} key={user.id}>
                <UserCard
                  user={user}
                  variant={variant}
                  onRemove={handleRemoveCandidate}
                />
              </Grid>
            ))}
            {selectedUsers.length < 4 && (
              <Grid size={3}>
                <UserCard
                  variant={variant}
                  setOpenCandidates={setOpenCandidates}
                />
              </Grid>
            )}
          </Grid>
        </Flex>
        {!!selectedUsers.length && (
          <>
            <CompareItem title={t('bio')}>
              <Grid container spacing={2} className="w-full mt-20">
                {isLoading
                  ? new Array(4).fill(1).map((_, i) => (
                      <Grid size={3} key={i}>
                        <Skeleton className="w-full h-[210px] rounded-[12px]" />
                      </Grid>
                    ))
                  : data?.map((user) => (
                      <Grid size={3} key={user.id} className="min-h-[210px]">
                        <Flex className="w-full  rounded-[12px] bg-popOverBg_white p-12 flex-1 h-full">
                          <Typography>
                            {user.bio ?? t('no_result_to_show')}
                          </Typography>
                        </Flex>
                      </Grid>
                    ))}
              </Grid>
            </CompareItem>
            <CompareItem title={t('overview')}>
              <Grid container spacing={2} className="w-full mt-20">
                {isLoading
                  ? new Array(4).fill(1).map((_, i) => (
                      <Grid size={3} key={i}>
                        <Flex className="w-full  rounded-[12px] bg-popOverBg_white p-12 flex-1 h-full gap-16">
                          <Skeleton className="w-full h-[40px] rounded-[12px]" />
                          <Skeleton className="w-full h-[40px] rounded-[12px]" />
                        </Flex>
                      </Grid>
                    ))
                  : data?.map((user) => (
                      <Grid size={3} key={user.id}>
                        <Flex className="w-full  rounded-[12px] bg-popOverBg_white p-8 flex-1 h-full">
                          <InfoCard
                            disabledHover
                            icon="envelope"
                            valueProps={{ color: 'primaryText' }}
                            title={t('email')}
                            subTitle={user.email}
                            iconClassName="!rounded-[4px]"
                          />
                          <InfoCard
                            disabledHover
                            icon="full-address"
                            valueProps={{ color: 'primaryText' }}
                            title={t('full_address')}
                            subTitle={
                              user.fullAddress ?? t('no_result_to_show')
                            }
                            iconClassName="!rounded-[4px]"
                          />
                        </Flex>
                      </Grid>
                    ))}
              </Grid>
            </CompareItem>
            <CompareItem title={t('expectations')}>
              <Grid container spacing={2} className="w-full mt-20">
                {isLoading
                  ? new Array(4).fill(1).map((_, i) => (
                      <Grid size={3} key={i}>
                        <Flex className="w-full  rounded-[12px] bg-popOverBg_white p-12 flex-1 h-full gap-16">
                          <Skeleton className="w-full h-[40px] rounded-[12px]" />
                          <Skeleton className="w-full h-[40px] rounded-[12px]" />
                        </Flex>
                      </Grid>
                    ))
                  : data?.map((user) => (
                      <Grid size={3} key={user.id}>
                        <Flex className="w-full  rounded-[12px] bg-popOverBg_white p-8 flex-1 h-full">
                          <InfoCard
                            disabledHover
                            icon="job-model"
                            valueProps={{ color: 'primaryText' }}
                            title={t('workplace_preference')}
                            subTitle={
                              user?.preferredEmploymentType?.label ??
                              t('no_result_to_show')
                            }
                            iconClassName="!rounded-[4px]"
                          />
                          <InfoCard
                            disabledHover
                            icon="job"
                            valueProps={{ color: 'primaryText' }}
                            title={t('job_type_preference')}
                            subTitle={
                              user?.preferredJobTitle?.label ??
                              t('no_result_to_show')
                            }
                            iconClassName="!rounded-[4px]"
                          />
                          <InfoCard
                            disabledHover
                            icon="signal-bar"
                            valueProps={{ color: 'primaryText' }}
                            title={t('job_type_preference')}
                            subTitle={
                              user.preferredExperienceLevel?.label ??
                              t('no_result_to_show')
                            }
                            iconClassName="!rounded-[4px]"
                          />
                          <InfoCard
                            disabledHover
                            icon="location-globe"
                            valueProps={{ color: 'primaryText' }}
                            title={t('job_type_preference')}
                            subTitle={
                              user.preferredLocation?.title ??
                              t('no_result_to_show')
                            }
                            iconClassName="!rounded-[4px]"
                          />
                          <InfoCard
                            disabledHover
                            icon="relocation-marker"
                            valueProps={{ color: 'primaryText' }}
                            title={t('relocation')}
                            subTitle={t(user.relocation ?? 'no_result_to_show')}
                            iconClassName="!rounded-[4px]"
                          />
                          <InfoCard
                            disabledHover
                            icon="salary-range"
                            valueProps={{ color: 'primaryText' }}
                            title={t('expected_salary_range_rangename')}
                            subTitle={
                              user.expectedSalaryPeriod?.label ??
                              t('no_result_to_show')
                            }
                            iconClassName="!rounded-[4px]"
                          />
                          <InfoCard
                            disabledHover
                            icon="calculator"
                            valueProps={{ color: 'primaryText' }}
                            title={t('tax_term')}
                            subTitle={
                              user.expectedTaxTermTitle?.label ??
                              t('no_result_to_show')
                            }
                            iconClassName="!rounded-[4px]"
                          />
                          <InfoCard
                            disabledHover
                            icon="money-increase"
                            valueProps={{ color: 'primaryText' }}
                            title={t('mark_up_percent')}
                            subTitle={
                              user.expectedMarkup
                                ? `${user.expectedMarkup} %`
                                : t('no_result_to_show')
                            }
                            iconClassName="!rounded-[4px]"
                          />
                        </Flex>
                      </Grid>
                    ))}
              </Grid>
            </CompareItem>
            <CompareItem title={t('skills')} recruiterData>
              <Grid container spacing={2} className="w-full mt-20">
                {isLoading
                  ? new Array(4).fill(1).map((_, i) => (
                      <Grid size={3} key={i}>
                        <Skeleton className="w-full h-[40px] rounded-[12px]" />
                      </Grid>
                    ))
                  : data?.map((user) => (
                      <Grid size={3} key={user.id}>
                        <Flex className="w-full  rounded-[12px] bg-popOverBg_white p-8 flex-1 h-full gap-12">
                          {user.skills.length ? (
                            user.skills.map((skill) => (
                              <ProgressItem
                                key={skill.id}
                                title={skill.name}
                                progressValue={skillLevelProgress[skill.level]}
                                tooltipText={t(skill?.level)}
                                progressSteps={4}
                              />
                            ))
                          ) : (
                            <Typography>{t('no_result_to_show')}</Typography>
                          )}
                        </Flex>
                      </Grid>
                    ))}
              </Grid>
            </CompareItem>
            <CompareItem title={t('languages')} recruiterData>
              <Grid container spacing={2} className="w-full mt-20">
                {isLoading
                  ? new Array(4).fill(1).map((_, i) => (
                      <Grid size={3} key={i}>
                        <Skeleton className="w-full h-[40px] rounded-[12px]" />
                      </Grid>
                    ))
                  : data?.map((user) => (
                      <Grid size={3} key={user.id}>
                        <Flex className="w-full  rounded-[12px] bg-popOverBg_white p-8 flex-1 h-full gap-12">
                          {user.languages.length ? (
                            user.languages.map((language) => (
                              <ProgressItem
                                key={language?.id}
                                title={language?.name}
                                progressValue={
                                  languageLevelProgress[
                                    language?.level as LanguageLevelType
                                  ]
                                }
                                progressSteps={7}
                                tooltipText={t(language?.level)}
                              />
                            ))
                          ) : (
                            <Typography>{t('no_result_to_show')}</Typography>
                          )}
                        </Flex>
                      </Grid>
                    ))}
              </Grid>
            </CompareItem>
            <CompareItem title={t('experience')}>
              <Grid container spacing={2} className="w-full mt-20">
                {isLoading
                  ? new Array(4).fill(1).map((_, i) => (
                      <Grid size={3} key={i}>
                        <Skeleton className="w-full h-[40px] rounded-[12px]" />
                      </Grid>
                    ))
                  : data?.map((user) => (
                      <Grid size={3} key={user.id}>
                        <Flex className="w-full  rounded-[12px] bg-popOverBg_white p-12 flex-1 h-full gap-12">
                          {user.experiences.length ? (
                            <AboutSectionLayout
                              data={user.experiences.reduce(
                                experienceNormalizer,
                                []
                              )}
                            >
                              {(props) => (
                                <AdvancedCardList {...props} showBrief />
                              )}
                            </AboutSectionLayout>
                          ) : (
                            <Typography>{t('no_result_to_show')}</Typography>
                          )}
                        </Flex>
                      </Grid>
                    ))}
              </Grid>
            </CompareItem>
          </>
        )}
      </ModalBody>
    </FixedRightSideModalDialog>
  );
};

export default CompareModal;

const skillLevelProgress: { [key in SkillLevelType]: number } = {
  BEGINNER: 1,
  INTERMEDIATE: 2,
  ADVANCED: 3,
  EXPERT: 4,
};

const languageLevelProgress: { [key in LanguageLevelType]: number } = {
  A1: 1,
  A2: 2,
  B1: 3,
  B2: 4,
  C1: 5,
  C2: 6,
  Native: 7,
};
