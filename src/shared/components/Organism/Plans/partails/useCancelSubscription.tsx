import React, { useCallback, type MouseEvent } from 'react';
import useOpenConfirm from '@shared/uikit/Confirmation/useOpenConfirm';
import { type ConfirmationProps } from '@shared/uikit/Confirmation/Confirmation';
import { cancelPlan } from '@shared/utils/api/page';
import useReactMutation from '@shared/utils/hooks/useReactMutation';
import useTranslation from '@shared/utils/hooks/useTranslation';
import Flex from '@shared/uikit/Flex';
import Typography from '@shared/uikit/Typography';
import InfoCard from '@shared/uikit/InfoCard';
import { type UseConfirmProps } from '@shared/uikit/types';
import {
  selectData,
  useNineDotPanelState,
} from '@shared/stores/nineDotPanelStore';
import { getPortal } from '@shared/utils/getAppEnv';
import useBusinessPage from '@shared/utils/hooks/useBusinessPage';
import type { PlansModalData } from '@shared/components/Organism/Plans/types';
import classes from './partials.module.scss';

export default function useCancelSubscription({
  onSuccess,
  onError,
  confirmProps = {},
}: {
  onSuccess?: () => void;
  onError?: () => void;
  confirmProps?: {
    variant?: ConfirmationProps['variant'];
    styles?: UseConfirmProps['styles'];
    isNarrow?: boolean;
  };
}) {
  const { t } = useTranslation();
  const { openConfirmDialog } = useOpenConfirm(confirmProps);

  const { timeSpan = 'MONTHLY' } =
    useNineDotPanelState<PlansModalData>(selectData);
  const portalName = getPortal().toUpperCase();
  const { data: businessPage } = useBusinessPage({ isEnabled: true });
  const { mutate: cancelSubscription } = useReactMutation({
    apiFunc: cancelPlan,
  });

  const handleCancel =
    (payload: {
      planName: string;
      portalName: string;
      timeSpan: string;
      page: string;
    }) =>
    (event?: MouseEvent<any>) => {
      openConfirmDialog({
        title: `${t('cancel_subscription')}?`,
        message: (
          <Flex className={classes.confirmationModalContentWrapper}>
            <Typography size={15}>{t('cancel_plan_message')}</Typography>
            <InfoCard
              classNames={{
                wrapper: classes.confirmationModalInfoCardWrapper,
                label: classes.confirmationModalInfoCardLabel,
              }}
              label={t('cancel_plan_hint')}
              labelProps={{
                size: 14,
                font: '400',
                color: 'secondaryDisabledText',
              }}
              leftIconProps={{
                size: 20,
                name: 'info-circle',
                color: 'secondaryDisabledText',
              }}
            />
          </Flex>
        ),
        confirmButtonText: t('confirm'),
        cancelButtonText: t('discard'),
        confirmCallback: async () => {
          cancelSubscription(payload, {
            onSuccess,
            onError,
          });
        },
      });
    };

  const onCancelSubscription = useCallback(
    (planName?: string) =>
      !businessPage?.id || !planName
        ? undefined
        : handleCancel({
            planName: planName.toUpperCase(),
            portalName,
            timeSpan,
            page: businessPage?.id,
          }),
    [businessPage?.id, portalName, timeSpan]
  );

  return { onCancelSubscription };
}
