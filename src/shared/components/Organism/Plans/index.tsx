import React, { useMemo, useState, type MouseEvent } from 'react';
import { getPortal } from '@shared/utils/getAppEnv';
import {
  selectData,
  setNineDotPanelState,
  useNineDotPanelState,
} from '@shared/stores/nineDotPanelStore';
import {
  isPremiumPlan,
  type Plan,
} from '@shared/utils/normalizers/plansNormalizer';
import useToast from '@shared/uikit/Toast/useToast';
import NineDotPanelSteps from '@shared/constants/NineDotPanelSteps';
import { useGetPlansData } from '@shared/utils/hooks/useGetPlansData';
import { enrollIntoPlan } from '@shared/utils/api/page';
import { getBusinessPageId } from '@shared/utils/hooks/useBusinessPage';
import {
  closeMultiStepForm,
  openMultiStepForm,
} from '@shared/hooks/useMultiStepForm';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { PlansModalData } from '@shared/components/Organism/Plans/types';
import type { NineDotsMainStepsType } from '@shared/components/Organism/NineDotPanel/types';
import PlansModal from './PlansModal';
import PlansModalExpanded from './PlansModalExpanded';
import useCancelSubscription from './partails/useCancelSubscription';
import { type OpenCheckoutModalProps } from '../MultiStepForm/CheckoutForm';

export default function Plans({
  onClose,
  handleNext,
}: {
  onClose?: (e: any) => void;
  handleNext?: (step?: NineDotsMainStepsType) => void;
}) {
  const [isBusy, setBusy] = useState(false);
  const toast = useToast();
  const { t } = useTranslation();
  const appPortal = getPortal().toUpperCase();

  const onOpenBilling = (e?: MouseEvent<any>) => {
    handleNext?.(NineDotPanelSteps.BILLINGS);
  };

  const {
    isExpanded,
    cart,
    timeSpan = 'MONTHLY',
  } = useNineDotPanelState<PlansModalData>(selectData);

  const { data, isLoading, refetch } = useGetPlansData({ appPortal, timeSpan });
  const hasPremiumPlan = useMemo(
    () => data?.find(({ price }) => isPremiumPlan({ price })) !== undefined,
    [data]
  );
  const onSeeMore = (plan?: Plan) => (event?: MouseEvent<any>) => {
    setNineDotPanelState({ data: { isExpanded: true, timeSpan } });
  };
  const onBack = () => {
    setNineDotPanelState({ data: { isExpanded: false, timeSpan } });
  };
  const onSelect = (plan?: Plan) => async (event?: MouseEvent<any>) => {
    if (!plan?.label) return;
    setBusy(true);
    try {
      const pageId = await getBusinessPageId();
      const planInvoice = await enrollIntoPlan({
        timeSpan,
        portalName: appPortal,
        planName: plan.label,
        numberOfSeats: 1,
      });
      setNineDotPanelState({ isOpen: false });
      setTimeout(
        () =>
          openMultiStepForm({
            formName: 'checkout',
            data: {
              entityData: {
                entityId: plan?.id,
                label: plan?.title,
                subTitle: t(timeSpan.toLowerCase()),
                priceUnit: plan?.priceUnit,
                requestType: 'NEW_PLAN',
                Logo: plan?.Logo,
              },
              invoice: {
                price: Number(planInvoice?.price),
                subtotal:
                  Number(planInvoice?.price) -
                  Number(planInvoice?.taxAmount || '0'),
                taxAmount: Number(planInvoice?.taxAmount),
                requestType: 'NEW_PLAN',
                id: planInvoice?.id,
              },
              actions: {
                onCancel: () => {
                  closeMultiStepForm('checkout');
                  setNineDotPanelState({
                    isOpen: true,
                    defaultActiveStep: NineDotPanelSteps.PLANS,
                    data: {
                      isExpanded,
                      cart,
                      timeSpan,
                    },
                  });
                },
                onSuccess: () => {
                  refetch();
                },
              },
            } as OpenCheckoutModalProps['data'],
          }),
        0
      );
    } finally {
      setBusy(false);
    }
  };

  const { onCancelSubscription } = useCancelSubscription({
    onSuccess: () => {
      toast({ message: 'success' });
      refetch();
    },
    confirmProps: {
      isNarrow: true,
      variant: 'normal',
    },
  });

  if (isLoading) return null;
  if (isExpanded)
    return (
      <PlansModalExpanded
        onBack={onBack}
        plans={data}
        onSelect={onSelect}
        hasPremiumPlan={hasPremiumPlan}
        onCancelSubscription={onCancelSubscription}
        handleOpenBillings={onOpenBilling}
        isBusy={isBusy}
      />
    );
  return (
    <PlansModal
      onClose={onClose}
      plans={data}
      onSeeMore={onSeeMore}
      onSelect={onSelect}
      hasPremiumPlan={hasPremiumPlan}
      onCancelSubscription={onCancelSubscription}
      handleOpenBillings={onOpenBilling}
    />
  );
}
