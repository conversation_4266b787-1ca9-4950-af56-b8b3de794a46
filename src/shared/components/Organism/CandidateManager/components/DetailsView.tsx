import type { FC } from 'react';
import CandidateCard, {
  CandidateCardSkeleton,
} from '@shared/components/molecules/CandidateCard';

import SectionLayout from '@shared/components/Organism/Objects/Common/Section.layout';
import { useObjectClicks } from '@shared/hooks/useObjectClicks';
import { RichTextView } from '@shared/uikit/RichText';
import useTranslation from '@shared/utils/hooks/useTranslation';
import Flex from '@shared/uikit/Flex';
import Button from '@shared/uikit/Button';
import EmbededView, { getEmbededDocumentLink } from '@shared/uikit/EmbededView';
import cleanRepeatedWords from '@shared/utils/toolkit/cleanRepeatedWords';
import { IsManualWrapper } from '@shared/components/molecules/IsManualWrapper';
import { useManagerContext } from '../CandidateManager.context';
import UploadCandidateResume from './FiltersBody/UploadCandidateResume';
import CandidateVsJobComparison from './CandidateVsJobComparison';

interface Props {
  className?: string;
}

export const CandidateManagerDetailsView: FC<Props> = ({ className }) => {
  const { t } = useTranslation();
  const { candidate, selectedSummary } = useManagerContext();
  const { embededSrc } = getEmbededDocumentLink(candidate?.resumeUrl ?? '');
  const { handleTagClick, handleHashtagClick, hoveredHashtag, onHashtagHover } =
    useObjectClicks();

  return (
    <Flex className={className}>
      {candidate ? (
        <CandidateCard
          avatar={candidate?.profile?.croppedImageUrl}
          firstText={candidate?.profile?.fullName}
          secondText={
            candidate?.profile?.usernameAtSign ??
            candidate?.profile?.email?.value
          }
          thirdText={candidate.profile?.occupation?.label}
          fourthText={cleanRepeatedWords(
            candidate?.profile?.location?.title || ''
          )}
          FirstTextWrapper={
            !candidate.profile.username ? IsManualWrapper : undefined
          }
        />
      ) : (
        <CandidateCardSkeleton showBadges showTags />
      )}
      {selectedSummary?.type !== 'ORIGINAL_CANDIDATE' && (
        <CandidateVsJobComparison />
      )}
      {candidate?.coverLetter ? (
        <SectionLayout title={t('cover_letter')}>
          <RichTextView
            html={candidate.coverLetter}
            typographyProps={{
              size: 15,
              color: 'thirdText',
              height: 16,
            }}
            showMore
            onMentionClick={handleTagClick}
            onHashtagClick={handleHashtagClick}
            onHashtagHover={onHashtagHover}
            hoveredHashtag={hoveredHashtag}
          />
        </SectionLayout>
      ) : null}
      <SectionLayout
        title={t('resume')}
        classNames={{ childrenWrap: 'min-h-[100px]' }}
        visibleActionButton={!!candidate?.resumeUrl}
        actionButton={
          <Flex flexDir="row" className="gap-4">
            <Button
              href={candidate?.resumeUrl}
              target="_blank"
              label={t('download')}
              leftIcon="download_2"
              leftType="far"
              disabled={!candidate?.resumeUrl}
              schema="gray-semi-transparent"
            />
            <Button
              href={embededSrc}
              target="_blank"
              label={t('view')}
              leftIcon="eye"
              disabled={!embededSrc}
              schema="semi-transparent"
            />
          </Flex>
        }
      >
        {candidate?.resumeUrl ? (
          <EmbededView src={candidate.resumeUrl} className="rounded" />
        ) : candidate ? (
          <UploadCandidateResume candidate={candidate} />
        ) : null}
      </SectionLayout>
      <div className="mb-5" />
    </Flex>
  );
};
