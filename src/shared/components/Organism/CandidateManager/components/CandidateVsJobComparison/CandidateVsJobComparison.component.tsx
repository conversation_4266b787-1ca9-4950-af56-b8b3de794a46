import { useManagerContext } from '@shared/components/Organism/CandidateManager/CandidateManager.context';
import ScheduleCardWrapper from '@shared/components/molecules/ScheduleCard/ScheduleCardWrapper';
import { SeeAllButtonDivider } from '@shared/components/molecules/SeeAllButtonDivider';
import Accordion from '@shared/uikit/Accordion';
import CandidateScoreBar from '@shared/uikit/CandidateScoreBar/CandidateScoreBar';
import Flex from '@shared/uikit/Flex';
import Icon, { IconName } from '@shared/uikit/Icon';
import Skeleton from '@shared/uikit/Skeleton';
import Typography from '@shared/uikit/Typography';
import { getMatchingScore } from '@shared/utils/api/jobs';
import { QueryKeys } from '@shared/utils/constants';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import useTranslation from '@shared/utils/hooks/useTranslation';
import languageNormalizer from '@shared/utils/normalizers/languageNormalizer';
import skillNormalizer from '@shared/utils/normalizers/skillNormalizer';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import React, { useMemo, useState } from 'react';
import CandidateVsJobComparisonExpectationList from './CandidateVsJobComparisonExpectationList';
import CandidateVsJobComparisonProgressList from './CandidateVsJobComparisonProgressList';

interface Props {
  participationId?: string;
}

const CandidateVsJobComparison: React.FC<Props> = () => {
  const { t } = useTranslation();
  const [seeMore, setSeeMore] = useState(false);
  const { selectedSummary } = useManagerContext();
  const participationId = selectedSummary?.id;

  const { data, isLoading } = useReactQuery({
    action: {
      apiFunc: getMatchingScore,
      key: [QueryKeys.getMatchingScore, participationId],
      params: participationId,
    },
    config: {
      enabled: !!participationId,
    },
  });

  const candidateExpectations: {
    title: string;
    value: string | number;
    icon: IconName;
  }[] = useMemo(
    () => [
      {
        title: t('workplace_preference'),
        value: data?.candidate?.preferredWorkPlaceType ?? t('not_set'),
        icon: 'job-model',
      },
      {
        title: t('job_type_preference'),
        value: data?.candidate?.preferredEmploymentType ?? t('not_set'),
        icon: 'briefcase-blank',
      },
      {
        title: t('exp_level'),
        value: data?.candidate?.preferredExperienceLevel ?? t('not_set'),
        icon: 'signal',
      },
      {
        title: t('preferred_location'),
        value: data?.candidate?.preferredLocation?.title ?? t('not_set'),
        icon: 'location-globe',
      },
      {
        title: t('relocation'),
        value: data?.candidate?.relocation ?? t('not_set'),
        icon: 'relocation-marker',
      },
      {
        title: translateReplacer(
          t('expected_salary_range_rangename'),
          t('yearly')
        ),
        value: data?.candidate?.expectedSalaryPeriod ?? t('not_set'),
        icon: 'salary-range',
      },
      {
        title: t('tax_term'),
        value: data?.candidate?.expectedTaxTerm ?? t('not_set'),
        icon: 'calculator',
      },
      {
        title: t('mark_up_percent'),
        value: data?.candidate?.expectedMarkup ?? t('not_set'),
        icon: 'money-increase',
      },
    ],
    [data]
  );

  const jobExpectations: {
    title: string;
    value: string | number;
    icon: IconName;
  }[] = useMemo(
    () => [
      {
        title: t('workplace_preference'),
        value: data?.job?.workPlaceType ?? t('not_set'),
        icon: 'job-model',
      },
      {
        title: t('job_type_preference'),
        value: data?.job?.employmentType ?? t('not_set'),
        icon: 'briefcase-blank',
      },
      {
        title: t('exp_level'),
        value: data?.job?.experienceLevel ?? t('not_set'),
        icon: 'signal',
      },
      {
        title: t('location'),
        value: data?.job?.location?.title ?? t('not_set'),
        icon: 'location-globe',
      },
      {
        title: t('relocation'),
        value: data?.job?.relocation ?? t('not_set'),
        icon: 'relocation-marker',
      },
      {
        title: translateReplacer(
          t('expected_salary_range_rangename'),
          t('yearly')
        ),
        value: data?.job?.salaryRangeMin ?? t('not_set'),
        icon: 'salary-range',
      },
      {
        title: t('tax_term'),
        value: data?.job?.taxTerm ?? t('not_set'),
        icon: 'calculator',
      },
      {
        title: t('mark_up_percent'),
        value: data?.job?.markup ?? t('not_set'),
        icon: 'money-increase',
      },
    ],
    [data]
  );

  const candidateSkills =
    data?.candidate?.profile?.skills.map(skillNormalizer) ?? [];
  const jobSkills = data?.job?.skills
    ? data?.job?.skills.map(skillNormalizer)
    : [];
  const candidateLangs =
    data?.candidate?.profile?.languages.map(languageNormalizer) ?? [];
  const jobLangs =
    (data?.job?.languages && data?.job?.languages.map(languageNormalizer)) ??
    [];

  if (isLoading) {
    return <Skeleton className="h-[100px] w-full mt-32 rounded-lg" />;
  }

  if (!data) return null;

  return (
    <Accordion
      singleOpen
      accordionRootClassName="mt-32"
      data={[
        {
          id: 1,
          header: ({ isExpanded }) => (
            <ScheduleCardWrapper
              classNames={{ root: isExpanded ? '!rounded-b-none' : '' }}
              indicatorProps={{
                className: '!h-[103%]',
                color: 'brand',
                size: 'lg',
              }}
            >
              <Flex className="!justify-between" flexDir="row">
                <Typography font="700" size={16} color="smoke">
                  {t('candidate_score_vs_job')}
                </Typography>
                <Icon
                  name="chevron-down"
                  color="brand"
                  className="cursor-pointer"
                />
              </Flex>
              <CandidateScoreBar score={data.score} showTitle={false} />
            </ScheduleCardWrapper>
          ),
          content: ({ isExpanded }) => (
            <ScheduleCardWrapper
              indicatorProps={{
                className: '!h-full',
                color: 'brand',
                size: 'lg',
              }}
              classNames={{ root: isExpanded ? '!rounded-t-none' : '' }}
            >
              <Flex flexDir="row" className="!justify-between">
                <CandidateVsJobComparisonExpectationList
                  expectationsListTitle={t('candidate_expectations')}
                  expectationsList={candidateExpectations}
                  progressListTitle={t('candidate_skills')}
                  progressList={candidateSkills}
                />
                <CandidateVsJobComparisonExpectationList
                  expectationsListTitle={t('job_expectations')}
                  expectationsList={jobExpectations}
                  progressListTitle={t('job_skills')}
                  progressList={jobSkills}
                />
              </Flex>
              <SeeAllButtonDivider
                onClick={() => {
                  setSeeMore(!seeMore);
                }}
                text={seeMore ? 'see_less' : 'see_more'}
                isOpen={seeMore}
              />
              {seeMore && (
                <Flex flexDir="row">
                  <CandidateVsJobComparisonProgressList
                    progressListTitle={t('candidate_languages')}
                    progressList={candidateLangs}
                    isLanguageList
                    hasWrapper
                    wrapperProps={{
                      classNames: {
                        root: '!w-full',
                        container: '!p-0',
                      },
                    }}
                  />

                  <CandidateVsJobComparisonProgressList
                    progressListTitle={t('job_languages')}
                    progressList={jobLangs}
                    isLanguageList
                    hasWrapper
                    wrapperProps={{
                      classNames: { root: 'w-full', container: '!pl-0 !pt-0' },
                    }}
                  />
                </Flex>
              )}
            </ScheduleCardWrapper>
          ),
        },
      ]}
    />
  );
};

export default CandidateVsJobComparison;
