import ProgressItem from '@shared/uikit/ProgressItem';
import Typography from '@shared/uikit/Typography';
import CardWrapper, {
  CardWrapperProps,
} from '@shared/components/molecules/CardItem/CardWrapper';

export type BaseProps = {
  progressListTitle: string;
  progressList: Skill[] | Language[];
};

export interface CandidateVsJobComparisonProgressListSkillsProps
  extends BaseProps {
  isLanguageList?: false;
  hasWrapper?: false;
  wrapperProps?: never;
}
export interface CandidateVsJobComparisonProgressListLanguagesProps
  extends BaseProps {
  isLanguageList?: true;
  hasWrapper?: true;
  wrapperProps?: CardWrapperProps;
}

const CandidateVsJobComparisonProgressList = (
  props:
    | CandidateVsJobComparisonProgressListSkillsProps
    | CandidateVsJobComparisonProgressListLanguagesProps
) => {
  const {
    progressListTitle,
    progressList,
    isLanguageList,
    wrapperProps = {},
    hasWrapper = false,
  } = props;

  const content = (
    <>
      <Typography font="500" size={14} mb={4} mt={4} color="muteMidGray">
        {progressListTitle}
      </Typography>
      {progressList.map((progress) => {
        const title = isLanguageList
          ? (progress as Language).name.label
          : (progress as Skill).name;

        const progressValue = progress?.value;

        const tooltipText =
          typeof progress.level === 'string'
            ? progress.level
            : progress.level?.label || progress.level?.value || undefined;

        return (
          <ProgressItem
            key={progress.id}
            title={title}
            progressValue={progressValue}
            progressSteps={4}
            tooltipText={tooltipText}
          />
        );
      })}
    </>
  );

  if (hasWrapper) {
    return <CardWrapper {...wrapperProps}>{content}</CardWrapper>;
  }

  return content;
};

export default CandidateVsJobComparisonProgressList;
