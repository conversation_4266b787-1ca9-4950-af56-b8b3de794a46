import ComingSoon from '@shared/svg/ComingSoon';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import useTranslation from '@shared/utils/hooks/useTranslation';
import dynamic from 'next/dynamic';
import { MessageProvider } from '@shared/components/Organism/Message/context/message/message.provider';
import TabViewSkeleton from './TabView.skeleton';

export type CandidateManagerTabkeys =
  | 'notes'
  | 'todos'
  | 'meetings'
  | 'documents'
  | 'reviews'
  | 'skillboard'
  | 'threads'
  | 'emails'
  | 'test';

const CandidateNotesTab = dynamic(() => import('./tab1.notes'), {
  loading: () => <TabViewSkeleton variant="notes" />,
});

const CandidateTodosTab = dynamic(() => import('./tab2.todos'), {
  loading: () => <TabViewSkeleton variant="todos" />,
});

const CandidateMeetingsTab = dynamic(() => import('./tab3.meetings'), {
  loading: () => <TabViewSkeleton variant="meetings" />,
});

const CandidateDocumentsTab = dynamic(() => import('./tab4.documents'), {
  loading: () => <TabViewSkeleton variant="documents" />,
});

const CandidateReviewsTab = dynamic(() => import('./tab5.reviews'), {
  loading: () => <TabViewSkeleton variant="reviews" />,
});
const CandidateThreadsTab = dynamic(
  () => import('./tab7.threads/CandidateThreadsTabHeader.component'),
  {
    loading: () => <TabViewSkeleton variant="reviews" />,
  }
);

const CandidateManagerSkillboard = dynamic(() => import('./tab6.skillboard'), {
  loading: () => <TabViewSkeleton variant="skillboard" />,
});

interface CandidateManagerTabsSwitchProps {
  active: CandidateManagerTabkeys;
}
export default function CandidateManagerPanels({
  active,
}: CandidateManagerTabsSwitchProps) {
  const { t } = useTranslation();
  switch (active) {
    case 'skillboard':
      return <CandidateManagerSkillboard />;
    case 'reviews':
      return <CandidateReviewsTab />;
    case 'documents':
      return <CandidateDocumentsTab />;
    case 'meetings':
      return <CandidateMeetingsTab />;
    case 'todos':
      return <CandidateTodosTab />;
    case 'threads':
      return (
        <MessageProvider initialValue={{ variant: 'page' }}>
          <CandidateThreadsTab />
        </MessageProvider>
      );
    case 'notes':
      return <CandidateNotesTab />;
    default: {
      return (
        <EmptySectionInModules
          isFullHeight
          title={t('coming_3dot')}
          text="Pending on some shared components to get ready"
          image={<ComingSoon />}
        />
      );
    }
  }
}
