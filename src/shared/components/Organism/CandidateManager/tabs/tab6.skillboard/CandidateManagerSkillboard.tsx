import Flex from '@shared/uikit/Flex';
import cnj from '@shared/uikit/utils/cnj';
import useTranslation from '@shared/utils/hooks/useTranslation';
import SkillboardItem, {
  Skillboard,
} from '@shared/components/molecules/SkillboardItem';
import UserInfo from '@shared/components/molecules/UserInfo';
import classes from '../tab.module.scss';
import { FooterWrap } from '@shared/components/Organism/CandidateManager/components/FooterWrap';
import SingleSkillPickerInput from '@shared/uikit/SkillPicker/SkillPickerInput';
import {
  addSkillboardScore,
  batchSetSkillboardScore,
  deleteSkillboardScore,
  updateSkillboardScore,
} from '@shared/utils/api/jobs';
import { useManagerContext } from '../../CandidateManager.context';
import IconButton from '@shared/uikit/Button/IconButton';
import useReactMutation from '@shared/utils/hooks/useReactMutation';
import TabViewSkeleton from '../TabView.skeleton';
import EmptySearchResult from '@shared/components/Organism/EmptySearchResult';
import InfoCard from '@shared/uikit/InfoCard';
import useResponseToast from '@shared/hooks/useResponseToast';
import { useFormikContext } from 'formik';
import SubmitButton from '@shared/uikit/Form/SubmitButton';
import Button from '@shared/uikit/Button';

export type SkillItem = {
  name: { label: string; value: string };
};

export type SkillboardFormValues = {
  skillInputValues: { name: string; level: string }[];
  editingId: string | null;
  skillScores: { id: string; score: number }[];
};

export default function CandidateManagerSkillboard({
  data,
  refetch,
  isLoading,
}: {
  data: any;
  refetch: () => void;
  isLoading: boolean;
}) {
  const { t } = useTranslation();
  const { handleSuccess, handleError } = useResponseToast();
  const { selectedSummary } = useManagerContext();
  const { values, setFieldValue } = useFormikContext<SkillboardFormValues>();

  const { mutate: deleteSkill } = useReactMutation({
    apiFunc: deleteSkillboardScore,
  });

  const { mutate: updateSkill, isLoading: isUpdateSkillLoading } =
    useReactMutation({
      apiFunc: updateSkillboardScore,
    });

  const { mutate: addSkill, isLoading: isAddSkillLoading } = useReactMutation({
    apiFunc: addSkillboardScore,
  });

  const onAddSkill = () => {
    setFieldValue('skillInputValues', [
      ...values.skillInputValues,
      { name: '', level: '' },
    ]);
  };

  const onChangeSkill = (value: { label: string; value: string }) => {
    setFieldValue('skillInputValues', [{ name: value.label, level: '' }]);
  };

  const onDiscardSkill = () => {
    setFieldValue('skillInputValues', []);
    setFieldValue('editingId', null);
  };

  const onEditSkill = (skill: Skillboard) => {
    setFieldValue('skillInputValues', [
      { name: skill.title, level: skill.level },
    ]);
    setFieldValue('editingId', skill.id);
  };

  const onDeleteSkill = (skillId: string) => {
    deleteSkill(skillId, {
      onSuccess: () => {
        refetch();
        handleSuccess()();
      },
      onError: () => {
        handleError();
      },
    });
  };

  const onSelectScore = (skill: { level: number; id: string }) => {
    const index = values.skillScores.findIndex(
      (skillScore: any) => skillScore.id === skill.id
    );
    if (index !== -1) {
      setFieldValue(
        'skillScores',
        values.skillScores.map((skillScore: any) =>
          skillScore.id === skill.id
            ? { ...skillScore, score: skill.level }
            : skillScore
        )
      );
    } else {
      setFieldValue('skillScores', [
        ...values.skillScores,
        { id: skill.id, score: skill.level },
      ]);
    }
  };

  const onSubmitSkill = (skill: SkillItem) => {
    if (selectedSummary?.id) {
      if (values.editingId) {
        updateSkill(
          {
            skillId: values.editingId,
            skill: skill.name,
            score: values.skillInputValues[0].level,
          },
          {
            onSuccess: () => {
              setFieldValue('skillInputValues', []);
              setFieldValue('editingId', null);
              refetch();
              handleSuccess()();
            },
            onError: () => {
              handleError();
            },
          }
        );
      } else {
        addSkill(
          {
            participationId: selectedSummary.id,
            skill: skill.name,
            score: 0,
          },
          {
            onSuccess: () => {
              setFieldValue('skillInputValues', []);
              refetch();
              handleSuccess()();
            },
            onError: () => {
              handleError();
            },
          }
        );
      }
    }
  };

  if (isLoading) return <TabViewSkeleton variant="skillboard" />;

  return (
    <>
      <Flex
        className={cnj(
          'sticky top-0 w-full h-[calc(100%-130px)]',
          classes.scrollArea
        )}
      >
        {data && (
          <UserInfo
            title={`${data?.lastModificationUser?.name} ${data?.lastModificationUser?.surname}`}
            description={data?.lastModificationUser?.occupationName}
            layoutTitle={t('last_modified_by')}
            image={data?.lastModificationUser?.croppedImageUrl}
          />
        )}
        {!isLoading &&
        (!data || data?.scores?.length === 0) &&
        values.skillInputValues?.length === 0 ? (
          <EmptySearchResult
            title={t('no_skill_board_yet')}
            sectionMessage={t('link_candidate_to_job_or_add_skills_manually')}
          />
        ) : (
          <>
            {data?.scores?.length > 0 && (
              <InfoCard
                hasLeftIcon
                leftIconProps={{
                  name: 'info-circle',
                  type: 'far',
                  size: 18,
                }}
                color="gray"
                label={t(
                  'review_and_score_the_listed_job_skills_or_add_manually'
                )}
              />
            )}

            <Flex className={classes.form}>
              {data?.scores?.map((score: any) => {
                if (values?.editingId === score.id) {
                  return (
                    <SingleSkillPickerInput
                      skillInputValues={values?.skillInputValues}
                      editingMode={true}
                      onConfirm={onSubmitSkill}
                      onDiscard={onDiscardSkill}
                      handleChange={onChangeSkill}
                      canClose={true}
                      selectedSkills={values?.skillInputValues}
                      isLoading={isUpdateSkillLoading || isAddSkillLoading}
                    />
                  );
                }
                return (
                  <SkillboardItem
                    key={score.id}
                    onEdit={onEditSkill}
                    onSelect={onSelectScore}
                    onDelete={onDeleteSkill}
                    activeScore={
                      values?.skillScores?.find(
                        (skillScore: any) => skillScore.id === score.id
                      )?.score ||
                      score?.score ||
                      0
                    }
                    item={{
                      title: score.skill,
                      level: score.score,
                      id: score.id,
                    }}
                  />
                );
              })}
              {values?.skillInputValues?.length > 0 && !values?.editingId && (
                <SingleSkillPickerInput
                  skillInputValues={values?.skillInputValues}
                  onDiscard={onDiscardSkill}
                  onConfirm={onSubmitSkill}
                  handleChange={onChangeSkill}
                  canClose={true}
                />
              )}
              <IconButton
                name="plus"
                size="md"
                type="fas"
                colorSchema="graySecondary"
                onClick={onAddSkill}
              />
            </Flex>
          </>
        )}
      </Flex>
      <FooterWrap className="!p-16">
        {!data || data?.scores?.length === 0 ? (
          <Button
            className="!w-full"
            label={t('add_manually')}
            leftIcon="plus"
            disabled={
              !(
                data ||
                (!values?.skillScores?.length && !!data?.scores?.length)
              )
            }
            leftSize={16}
            onClick={onAddSkill}
          />
        ) : (
          <SubmitButton
            className="!w-full"
            label={t('save')}
            isLoading={values?.skillScores?.length === 0}
            leftSize={16}
          />
        )}
      </FooterWrap>
    </>
  );
}
