import Form from '@shared/uikit/Form';
import {
  batchSetSkillboardScore,
  getSkillboardScore,
} from '@shared/utils/api/jobs';
import React from 'react';
import CandidateManagerSkillboard from './CandidateManagerSkillboard';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import { QueryKeys } from '@shared/utils/constants';
import { useManagerContext } from '../../CandidateManager.context';
import useResponseToast from '@shared/hooks/useResponseToast';

const CandidateManagerSkillboardContainer = () => {
  const { selectedSummary } = useManagerContext();
  const { handleSuccess, handleError } = useResponseToast();

  const { data, refetch, isLoading } = useReactQuery({
    action: {
      key: [QueryKeys.getSkillboardScore, selectedSummary?.id],
      apiFunc: () =>
        getSkillboardScore({ participationId: selectedSummary?.id }),
    },
    config: {
      enabled: !!selectedSummary?.id,
    },
  });

  const onSuccess = () => {
    refetch();
    handleSuccess()();
  };

  return (
    <Form
      initialValues={{ skillInputValues: [], editingId: null, skillScores: [] }}
      className="h-full"
      apiFunc={batchSetSkillboardScore}
      transform={({ skillScores }) =>
        skillScores.map((skill) => ({
          id: skill.id,
          score: skill.score,
        }))
      }
      onSuccess={onSuccess}
      onFailure={handleError}
    >
      {() => (
        <CandidateManagerSkillboard
          data={data}
          refetch={refetch}
          isLoading={isLoading}
        />
      )}
    </Form>
  );
};

export default CandidateManagerSkillboardContainer;
