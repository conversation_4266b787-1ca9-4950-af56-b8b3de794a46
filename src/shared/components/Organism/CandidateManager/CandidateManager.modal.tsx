import type { FC } from 'react';

import {
  useGlobalDispatch,
  useGlobalState,
} from '@shared/contexts/Global/global.provider';

import FixedRightSideModal from 'shared/uikit/Modal/FixedRightSideModalDialog';
import ModalBody from 'shared/uikit/Modal/ModalBody';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import CandidateManagerLayout from './CandidateManager.layout';
import CandidateManagerProvider from './CandidateManager.provider';

interface Props {
  participationId: string;
}

const CandidateManagerModal: FC<Props> = () => {
  const candidateManager = useGlobalState('candidateManager');
  const appDispatch = useGlobalDispatch();
  const { allParams } = useCustomParams();
  const { currentEntityId } = allParams;
  const id = candidateManager.enableNavigate
    ? candidateManager.entity
      ? allParams[candidateManager.entity]
      : currentEntityId
    : candidateManager.id;
  const { tab } = candidateManager;
  const onClose = () => {
    appDispatch({ type: 'TOGGLE_CANDIDATE_MANAGER' });
  };

  if (!id) return null;
  return (
    <FixedRightSideModal
      wide
      doubleColumn
      onBack={onClose}
      onClose={onClose}
      isOpenAnimation
      contentClassName="!max-w-full !bg-tooltipText overflow-hidden !border-l-0"
    >
      <ModalBody className="overflow-hidden">
        <CandidateManagerProvider id={id} selectedTab={tab}>
          <CandidateManagerLayout />
        </CandidateManagerProvider>
      </ModalBody>
    </FixedRightSideModal>
  );
};

export default CandidateManagerModal;
