import React from 'react';
import translateReplacer from 'shared/utils/toolkit/translateReplacer';
import { ToggleNotificationList } from 'shared/utils/constants/NotificationVariants';
import useTranslation from 'shared/utils/hooks/useTranslation';
import type { INotificationProps } from '../Notification.layout';
import Layout, { IconSvg as Icon } from '../Notification.layout';
import { renderNotificationText } from '../utils/renderNotificationText';
import { routeNames } from '@shared/utils/constants';
import useHistory from '@shared/utils/hooks/useHistory';

type OtherProps = {
  userId: string;
  userTitle: string;
};

const CandidateJoined: React.FC<INotificationProps<OtherProps>> = (props) => {
  const { data, onSeen, menuActions } = props;
  const { t } = useTranslation();
  const history = useHistory();

  const handleView = () => {
    onSeen?.();
    if (data?.userId) history.push(routeNames.candidate.makeRoute(data.userId));
  };
  const hasToggleNotification = ToggleNotificationList.includes(data.type);

  const description = renderNotificationText(
    translateReplacer(t('person_signed_up_within_your_candidate_invitation'), [
      data?.userTitle,
    ]),
    [data?.userTitle]
  );

  return (
    <Layout
      hasToggleNotification={hasToggleNotification}
      onClick={handleView}
      menuActions={menuActions}
      icon={<Icon iconName="bell-on-s" type="far" color="brand" />}
      description={description}
      primaryAction={{
        closeModal: true,
        label: t('view_candidate'),
        onClick: handleView,
      }}
      date={data?.createdDate}
      seen={data?.seen}
    />
  );
};

export default CandidateJoined;
