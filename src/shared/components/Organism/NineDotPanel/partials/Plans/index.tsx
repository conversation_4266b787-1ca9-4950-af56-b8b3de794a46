import React, { useEffect, useMemo, useState } from 'react';
import type { PageAccessibilityType } from '@shared/types/page';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { getBusinessPageId } from '@shared/utils/hooks/useBusinessPage';
import Skeleton from '@shared/uikit/Skeleton';
import Typography from '@shared/uikit/Typography';
import Flex from '@shared/uikit/Flex';
import PlansPage from '@shared/components/Organism/Plans';
import type { NineDotsMainStepsType } from '@shared/components/Organism/NineDotPanel/types';
import classes from './index.module.scss';

interface Props {
  data: PageAccessibilityType[];
  onClose?: (e: any) => void;
  handleNext?: (step?: NineDotsMainStepsType) => void;
  isLoadingAccess: boolean;
}

const Plans = ({ data, onClose, handleNext, isLoadingAccess }: Props) => {
  const [businessPageId, setBusinessPageId] = useState<string | undefined>(
    undefined
  );
  const [isLoading, setLoading] = useState<boolean>(isLoadingAccess);
  useEffect(() => {
    setLoading(true);
    getBusinessPageId()
      .then((pageId?: string) => {
        setBusinessPageId(pageId);
      })
      .finally(() => setLoading(false));
  }, []);

  const hasAccess = useMemo(() => {
    if (isLoading || isLoadingAccess) return false;
    if (!businessPageId) return false;
    const currentPageMembership = data?.find(
      (el) => el.username === businessPageId
    )?.pageMemberships;
    return currentPageMembership?.some(
      ({ role, status }) =>
        status === 'ACCEPTED' && ['ADMIN', 'OWNER'].includes(role)
    );
  }, [businessPageId, data, isLoading, isLoadingAccess]);

  const { t } = useTranslation();
  if (isLoading || isLoadingAccess)
    return <Skeleton className="!h-full w-full" />;
  if (hasAccess) return <PlansPage onClose={onClose} handleNext={handleNext} />;
  return (
    <Flex className={classes.noPermissionContainer}>
      <Typography align="center" size={16} font="700">
        {t('youre_not_admin_or_owner')}
      </Typography>
      <Typography align="center" size={15} font="400">
        {t('for_premium_contact_admin')}
      </Typography>
    </Flex>
  );
};

export default Plans;
