import React, {
  type FC,
  type RefObject,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  min,
  max,
  floor,
  map,
  filter,
  debounce,
  isEqual,
  chunk,
  range,
} from 'lodash';
import type AvatarEditor from 'react-avatar-editor';
import { FastAverageColor } from 'fast-average-color';
import { Carousel } from 'react-responsive-carousel';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import SimpleSlider from 'shared/uikit/SimpleSlider';
import convertBase64ToBlob from 'shared/utils/toolkit/convertBase64ToBlob';
import convertBlobToFile from 'shared/utils/toolkit/convertBlobToFile';
import getImageData from 'shared/utils/toolkit/getImageData';
import { useGlobalDispatch } from 'shared/contexts/Global/global.provider';
import useResizeWindow from 'shared/utils/hooks/useResizeWindow';
import useMedia from 'shared/uikit/utils/useMedia';
import ModalFooter from '@shared/uikit/Modal/ModalFooter';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import Button from '@shared/uikit/Button';
import useTranslation from '@shared/utils/hooks/useTranslation';
import IconButton from '@shared/uikit/Button/IconButton';
import {
  useCreatePostDispatch,
  useCreatePostState,
} from '../../../context/createPost.provider';
import { CreatePostMediaFile } from '../../../createPost.utils';
import MainCarouselItem from './MainCarouselItem.component';
import BottomCarouselSlides from './BottomCarouselSlides.component';
import RatioActions from './RatioActions.component';
import classes from './MediaItems.component.module.scss';

type Ratio = '0:0' | '1:1' | '4:5' | '16:9';
type Position = { x: number; y: number };
type Size = { width: number; height: number };

type ReadyToUpload = {
  original: any;
  file: File | Blob | null;
  position: Position;
  ratio: Ratio;
  zoom: number;
  size: Size;
  realSize: Size;
  backgroundColor: string;
};

const mediaMaxWidth = 800;
const widthBreakpoints = [0, 650, 750];
const fac = new FastAverageColor();

const MediaItems: FC<{
  onSubmit?: () => void;
  onCancel?: () => void;
  minZoom?: number;
  maxZoom?: number;
  zoomMovementStep?: number;
}> = ({
  onCancel,
  onSubmit,
  minZoom = 1,
  maxZoom = 3,
  zoomMovementStep = 0.1,
}) => {
  const { t } = useTranslation();
  const dispatch = useCreatePostDispatch();
  const uploadSubmitted = useCreatePostState('uploadSubmitted');
  const cropperData = useCreatePostState('cropperData');
  const files: CreatePostMediaFile[] = useCreatePostState('files');
  const oldReadyToUploadFiles: ReadyToUpload[] =
    useCreatePostState('readyToUploadFiles');
  const appDispatch = useGlobalDispatch();

  const firstFile = useMemo(
    () => (files?.length ? files[0] : ({} as CreatePostMediaFile)),
    [files]
  );

  const filesCountRef = useRef(0);
  const isRemovingRef = useRef(false);
  const wrapperRef = useRef<HTMLDivElement>(null);
  const readyToUploadFiles = useRef<ReadyToUpload[]>([]);
  const hiddenCroppersRef = useRef<AvatarEditor[]>([]);
  const videosRef = useRef<{
    [key: string]: RefObject<HTMLVideoElement>;
  }>({});

  const { isTabletAndLess } = useMedia();

  const [wrapperSide, setWrapperSide] = useState(0);
  const [wrapperRatio, setWrapperRatio] = useState<Ratio>(
    isTabletAndLess ? '1:1' : cropperData.ratio
  );
  const [firstItemRealSize, setFirstItemRealSize] = useState({
    width: 0,
    height: 0,
  });
  const [cropperSize, setCropperSize] = useState({
    width: cropperData.width,
    height: cropperData.height,
  });
  const [hiddenCropperSize, setHiddenCropperSize] = useState({
    width: 0,
    height: 0,
  });
  const [croppersPositions, setCroppersPositions] = useState<Position[]>([]);
  const [croppersZooms, setCroppersZooms] = useState<number[]>([]);
  const [croppersBackgroundColors, setCroppersBackgroundColors] = useState<
    string[]
  >([]);
  const [mainSliderIndex, setMainSliderIndex] = useState(0);
  const [bottomSliderIndex, setBottomSliderIndex] = useState(0);
  const [videosSizes, setVideosSizes] = useState<Size[]>([]);
  useEffect(() => {
    if (!oldReadyToUploadFiles.length) {
      return;
    }

    const positions: Position[] = [];
    const zooms: number[] = [];
    const colors: string[] = [];

    oldReadyToUploadFiles.forEach(({ position, zoom, backgroundColor }) => {
      positions.push(position);
      zooms.push(zoom);
      colors.push(backgroundColor);
    });

    setCroppersPositions(positions);
    setCroppersZooms(zooms);
    setCroppersBackgroundColors(colors);
  }, [oldReadyToUploadFiles]);

  useEffect(() => {
    if (files.length > filesCountRef.current) {
      setBottomSliderIndex(() => files.length - 1);
    }
    if (files.length < filesCountRef.current) {
      isRemovingRef.current = false;
    }
    filesCountRef.current = files.length;
  }, [files.length]);

  /*
    the real size and '0:0' ratio
    calculating based on width of first image
  */
  useEffect(() => {
    if (!firstFile?.url) {
      return;
    }

    if (firstFile?.type === 'image') {
      getImageData(firstFile.url).then((data) =>
        setFirstItemRealSize({
          width: data.width,
          height: data.height,
        })
      );
    }
  }, [firstFile]);

  const setVideoRef = (index: number) => (ref: RefObject<HTMLVideoElement>) => {
    if (!ref.current) return;
    ref.current.loop = true;
    ref.current.playsInline = true;

    videosRef.current = {
      ...videosRef.current,
      [index]: ref,
    };
  };

  const setHiddenCroppersRef = (index: number) => (instance: AvatarEditor) => {
    hiddenCroppersRef.current[index] = instance;
  };

  const handleChangeFile = async ({
    index,
    base64,
    originalFile,
    position,
    zoom,
    ratio,
    size,
  }: {
    index: number;
    base64: string;
    originalFile: CreatePostMediaFile;
    position: Position;
    zoom: number;
    ratio: Ratio;
    size: {
      width: number;
      height: number;
    };
  }) => {
    try {
      if (!base64) return;
      const color = await fac.getColorAsync(base64, {
        algorithm: 'dominant',
      });
      setCroppersBackgroundColors((colors) => {
        if (color.hex === colors[index]) {
          return colors;
        }
        const updatedColors = [...colors];
        updatedColors[index] = color.hex;
        return updatedColors;
      });
      const convertedBlob = await convertBase64ToBlob(base64);
      const file = convertBlobToFile(convertedBlob);
      if (file)
        readyToUploadFiles.current[index] = {
          file,
          original: originalFile,
          position: position || { x: 0.5, y: 0.5 },
          zoom: zoom || 1,
          ratio,
          size,
          realSize: { width: 0, height: 0 },
          backgroundColor: color.hex,
        };
    } catch {
      return;
    }
  };

  const handleImageChange = (index: number) => () => {
    handleChangeFile({
      index,
      base64: hiddenCroppersRef.current[index]
        ?.getImageScaledToCanvas()
        ?.toDataURL('image/jpeg'),
      originalFile: files[index],
      position: croppersPositions[index],
      zoom: croppersZooms[index],
      ratio: wrapperRatio,
      size: hiddenCropperSize,
    });
  };

  const handleCropperPositionChange =
    (index: number) => (position: Position) => {
      setCroppersPositions((positions) => {
        if (isEqual(position, positions[index])) {
          return positions;
        }
        const updatedPositions = [...positions];
        updatedPositions[index] = position;
        return updatedPositions;
      });
    };

  const handleChangeZoom = useCallback(
    (zoom: number) => {
      setCroppersZooms((zooms) => {
        const updateZooms = [...zooms];
        updateZooms[mainSliderIndex] = zoom;
        return updateZooms;
      });
    },
    [mainSliderIndex]
  );

  const handlePlusZoom = () => {
    setCroppersZooms((zooms) => {
      const updateZooms = [...zooms];
      const z = updateZooms[mainSliderIndex] || 1;
      updateZooms[mainSliderIndex] = z < maxZoom ? z + zoomMovementStep : z;
      return updateZooms;
    });
  };

  const handleMinusZoom = () => {
    setCroppersZooms((zooms) => {
      const updateZooms = [...zooms];
      const z = updateZooms[mainSliderIndex] || 1;
      updateZooms[mainSliderIndex] = z > minZoom ? z - zoomMovementStep : z;
      return updateZooms;
    });
  };

  useEffect(() => {
    if (files[bottomSliderIndex]) {
      setMainSliderIndex(bottomSliderIndex);
    }
  }, [bottomSliderIndex, files]);

  const resizeEventListener = useMemo(
    () =>
      debounce(() => {
        if (wrapperRef.current) {
          setWrapperSide(wrapperRef.current?.offsetWidth);
        }
      }, 100),
    []
  );

  useResizeWindow(resizeEventListener, true);

  useEffect(() => {
    if (!firstItemRealSize.width || !firstItemRealSize.height || !wrapperSide)
      return;

    let cropperWidth = wrapperSide;
    let cropperHeight = wrapperSide;

    if (wrapperRatio === '0:0') {
      if (firstItemRealSize.width > firstItemRealSize.height) {
        cropperHeight =
          (firstItemRealSize.height *
            (croppersZooms?.[mainSliderIndex] ?? 1) *
            wrapperSide) /
          firstItemRealSize.width;
      }
      if (firstItemRealSize.width < firstItemRealSize.height) {
        cropperWidth = min([
          (firstItemRealSize.width *
            (croppersZooms?.[mainSliderIndex] ?? 1) *
            wrapperSide) /
            firstItemRealSize.height,
          wrapperSide < widthBreakpoints[1]
            ? wrapperSide - 80
            : widthBreakpoints[1],
        ]);
      }
    }
    // 4:5
    if (wrapperRatio === '4:5') {
      cropperWidth = (4 * wrapperSide) / 5;
    }
    if (wrapperRatio === '16:9') {
      cropperHeight = (9 * wrapperSide) / 16;
    }

    setCropperSize({
      width: floor(cropperWidth),
      height: floor(cropperHeight),
    });
  }, [
    wrapperRatio,
    wrapperSide,
    firstItemRealSize.width,
    firstItemRealSize.height,
    croppersZooms?.[mainSliderIndex],
  ]);

  // set hidden cropper width and height
  useEffect(() => {
    let scaledWidth = 650;

    if (firstItemRealSize.width > 750) {
      scaledWidth = mediaMaxWidth;
    } else if (firstItemRealSize.width > 650) {
      scaledWidth = 650;
    }

    let cropperWidth = scaledWidth;
    let cropperHeight = scaledWidth;

    if (wrapperRatio === '0:0') {
      cropperHeight =
        (cropperWidth * firstItemRealSize.height) / firstItemRealSize.width;
      if (firstItemRealSize.width < firstItemRealSize.height) {
        cropperHeight =
          min([cropperHeight, mediaMaxWidth]) || cropperHeight || mediaMaxWidth;
        cropperWidth =
          (firstItemRealSize.width * mediaMaxWidth) / firstItemRealSize.height;
      }
    }
    if (wrapperRatio === '4:5') {
      cropperHeight = (scaledWidth * 5) / 4;
    }
    if (wrapperRatio === '16:9') {
      cropperHeight = (scaledWidth * 9) / 16;
    }

    setHiddenCropperSize(() => ({
      width: floor(cropperWidth),
      height: floor(cropperHeight),
    }));
  }, [wrapperRatio, firstItemRealSize.width, firstItemRealSize.height]);

  useEffect(() => {
    if (
      files[mainSliderIndex]?.type !== 'video' ||
      videosSizes[mainSliderIndex]
    ) {
      return;
    }

    const video = videosRef.current[mainSliderIndex].current;

    if (video)
      video.onloadeddata = () => {
        setTimeout(() => {
          setVideosSizes((sizes) => {
            const updatedSizes = [...sizes];
            updatedSizes[mainSliderIndex] = {
              width: video.offsetWidth,
              height: video.offsetHeight,
            };
            return updatedSizes;
          });
        }, 100);
      };
  }, [files, mainSliderIndex, videosSizes]);

  useEffect(() => {
    files.forEach((file, ii: number) => {
      if (file.type !== 'video' || !videosSizes[ii]) {
        return;
      }

      const videoWidth = videosSizes[ii].width;
      const videoHeight = videosSizes[ii].height;

      const realSize = {
        width: videoWidth,
        height: videoHeight,
      };

      if (!ii) {
        setFirstItemRealSize(realSize);
      }

      const zoom =
        max([
          cropperSize.width / (videoWidth || 1),
          cropperSize.height / (videoHeight || 1),
        ]) || 1;

      setCroppersZooms((zooms) => {
        const updateZooms = [...zooms];
        if (zoom) updateZooms[ii] = zoom;
        return updateZooms;
      });

      readyToUploadFiles.current[ii] = {
        ...file,
        original: file,
        position: croppersPositions[ii],
        zoom,
        ratio: wrapperRatio,
        size: hiddenCropperSize,
        realSize,
        backgroundColor: '#ffffffff',
      };
    });
  }, [
    wrapperRatio,
    cropperSize,
    videosSizes,
    croppersPositions,
    hiddenCropperSize,
    files,
  ]);

  useEffect(() => {
    if (!uploadSubmitted) {
      return;
    }

    dispatch({
      type: 'SET_READY_TO_UPLOAD_FILES',
      payload: { files: readyToUploadFiles.current },
    });
    dispatch({
      type: 'SET_CROPPER_DATA',
      payload: {
        cropperData: {
          width: cropperSize.width,
          height: cropperSize.height,
          ratio: wrapperRatio,
          wrapperSide,
        },
      },
    });
    dispatch({
      type: 'SET_UPLOAD_SUBMITTED',
      payload: { uploadSubmitted: false },
    });
    appDispatch({
      type: 'SET_CREATE_POST_MODAL',
      payload: {
        isOpenModal: true,
        currentTab: 'main',
      },
    });
  }, [
    uploadSubmitted,
    readyToUploadFiles,
    cropperSize.width,
    cropperSize.height,
    wrapperRatio,
    wrapperSide,
  ]);

  const numberOfVisibleThumbnails = floor(wrapperSide / 100);

  return (
    <>
      <ModalBody>
        <Flex className={classes.singleFileContainer}>
          <Flex
            ref={wrapperRef}
            className={classes.wrapper}
            style={{ height: wrapperSide ? `${wrapperSide}px` : 'auto' }}
          >
            {/*{!files.length && <Skeleton className={classes.skeleton} />}*/}
            <Carousel
              showIndicators={false}
              swipeable={false}
              showThumbs={false}
              showStatus={false}
              showArrows={false}
              className={classes.carousel}
              selectedItem={mainSliderIndex}
            >
              {map(files, (file, ii: number) => (
                <MainCarouselItem
                  key={file.id || ii}
                  file={file}
                  firstItemSize={firstItemRealSize}
                  cropperSize={cropperSize}
                  hiddenCropperSize={hiddenCropperSize}
                  wrapperRatio={wrapperRatio}
                  position={croppersPositions[ii]}
                  zoom={croppersZooms[ii]}
                  backgroundColor={croppersBackgroundColors[ii]}
                  isPreparing={
                    Boolean(videosSizes[mainSliderIndex]) &&
                    !videosSizes[mainSliderIndex]?.width
                  }
                  onPositionChange={handleCropperPositionChange(ii)}
                  onImageChange={handleImageChange(ii)}
                  setHiddenCroppersRef={setHiddenCroppersRef(ii)}
                  setVideoRef={setVideoRef(ii)}
                />
              ))}
            </Carousel>
          </Flex>
        </Flex>
      </ModalBody>
      <ModalFooter>
        <Flex className={cnj(classes.actionsContainer, 'gap-8 mb-8_12')}>
          <IconButton
            type="far"
            name="minus"
            onClick={handleMinusZoom}
            colorSchema="semi-transparent"
            variant="rectangle"
          />
          <SimpleSlider
            value={croppersZooms[mainSliderIndex] || 1}
            min={1}
            max={2}
            step={0.05}
            onChange={handleChangeZoom}
            className={cnj(
              classes.slider,
              files[mainSliderIndex]?.type !== 'image' && classes.disabledSlider
            )}
            thumbClassName={classes.thumbClassName}
            trackClassName={classes.trackClassName}
          />
          <IconButton
            type="far"
            name="plus"
            onClick={handlePlusZoom}
            colorSchema="semi-transparent"
            variant="rectangle"
          />
        </Flex>
        <RatioActions
          setWrapperRatio={setWrapperRatio}
          wrapperRatio={wrapperRatio}
          // disabled={files[mainSliderIndex]?.type !== 'image'}
        />
        <Flex className={cnj(classes.actionsContainer, 'mt-16_20')}>
          {/* bottom slider carousel */}
          <Carousel
            swipeable
            emulateTouch
            showArrows={false}
            showIndicators={false}
            showThumbs={false}
            showStatus={false}
            autoPlay={false}
            // a hack! to prevent autoplay
            interval={60 * 60 * 60 * 24}
            className={cnj(classes.carousel)}
            swipeScrollTolerance={10}
            preventMovementUntilSwipeScrollTolerance
          >
            {map(
              chunk(range(files.length + 1), numberOfVisibleThumbnails),
              (filesIndexes, ii) => (
                <Flex
                  key={ii}
                  className={classes.bottomSliderWrapper}
                  flexDir="row"
                >
                  <BottomCarouselSlides
                    slideIndexes={filesIndexes}
                    slideCount={numberOfVisibleThumbnails}
                    bottomSliderIndex={bottomSliderIndex}
                    onClickOnItem={(fileIndex) => {
                      setBottomSliderIndex(fileIndex);
                    }}
                    onRemove={(fileIndex) => {
                      isRemovingRef.current = true;

                      setCroppersPositions((positions) =>
                        positions.filter((__, jj) => jj !== fileIndex)
                      );
                      setCroppersZooms((zooms) =>
                        zooms.filter((__, jj) => jj !== fileIndex)
                      );
                      setVideosSizes((sizes) =>
                        sizes.filter((__, jj) => jj !== fileIndex)
                      );

                      const nextIndex =
                        mainSliderIndex === files.length - 1
                          ? bottomSliderIndex - 1
                          : bottomSliderIndex;

                      setBottomSliderIndex(() =>
                        nextIndex < 0 ? 0 : nextIndex
                      );
                      readyToUploadFiles.current = filter(
                        readyToUploadFiles.current,
                        (__, jj) => jj !== fileIndex
                      );
                      dispatch({
                        type: 'DELETE_FILE_BY_INDEX',
                        payload: { index: fileIndex },
                      });
                    }}
                  />
                </Flex>
              )
            )}
          </Carousel>
        </Flex>
        <Flex className="!flex-row gap-8 pt-16_20">
          <Button
            onClick={() => {
              onCancel?.();
            }}
            label={t('cancel')}
            schema="ghost"
            fullWidth
          />
          <Button
            onClick={() => {
              dispatch({
                type: 'SET_READY_TO_UPLOAD_FILES',
                payload: {
                  files: readyToUploadFiles.current,
                },
              });
              onSubmit?.();
            }}
            schema="primary-blue"
            // disabled={cannotUpload}
            label={t('done')}
            fullWidth
          />
        </Flex>
      </ModalFooter>
    </>
  );
};

export default MediaItems;
