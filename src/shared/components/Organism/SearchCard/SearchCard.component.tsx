import React from 'react';
import type { TextProps } from 'shared/uikit/Typography';
import Avatar from 'shared/uikit/Avatar';
import cnj from 'shared/uikit/utils/cnj';
import type { FlexProps } from 'shared/uikit/Flex';
import Flex from 'shared/uikit/Flex';
import ObjectInfoCard from 'shared/components/molecules/ObjectInfoCard';
import SearchCardWrapper from './SearchCard.wrapper';
import classes from './SearchCard.component.module.scss';

export interface SearchCardProps extends FlexProps {
  imgSrc: string;
  isPage?: boolean;
  className?: string;
  isSelected?: boolean;
  isHoverAble?: boolean;
  bottomComponent?: () => React.ReactElement;
  topRightActionComponent?: React.ReactElement;
  backgroundColor?: string;
  borderColor?: string;
  firstText: string;
  secondText: string;
  thirdText: string;
  fourthText: string | React.ReactNode;
  thirdTextProps?: TextProps;
  classNames?: {
    bottomWrapper?: string;
    container?: string;
    wrapper?: string;
  };
  onClickAvatar?: VoidFunction;
}

const SearchCard = ({
  imgSrc,
  isPage,
  className,
  isSelected,
  isHoverAble = true,
  bottomComponent,
  topRightActionComponent,
  backgroundColor = 'brand_4',
  borderColor = 'brand',
  firstText,
  secondText,
  thirdText,
  fourthText,
  classNames,
  onClickAvatar,
  ...rest
}: SearchCardProps): JSX.Element => (
  <SearchCardWrapper
    {...rest}
    backgroundColor={backgroundColor}
    isHoverAble={isHoverAble}
    borderColor={borderColor}
    className={className}
    isSelected={isSelected}
  >
    <Flex
      className={cnj(classes.searchCardCardContainer, classNames?.container)}
    >
      <Flex
        flexDir="row"
        className={cnj(
          // !!topRightActionComponent && classes.wrapper,
          classNames?.wrapper
        )}
        onClick={onClickAvatar}
      >
        <Avatar
          imgSrc={imgSrc}
          className={classes.avatar}
          isCompany={isPage}
          size="flg"
        />
        <Flex
          className={cnj(
            classes.searchCardInfoWrapper,
            !!topRightActionComponent && classes.searchCardInfoWrapperWithSaved
          )}
        >
          <ObjectInfoCard
            firstText={firstText}
            secondText={secondText}
            thirdText={thirdText}
            fourthText={fourthText}
            isPage={isPage}
            isFirstTextSmall
            withAvatar={false}
          />
        </Flex>
        {topRightActionComponent}
      </Flex>
      {bottomComponent && (
        <Flex className={cnj(classes.bottomWrapper, classNames?.bottomWrapper)}>
          {bottomComponent()}
        </Flex>
      )}
    </Flex>
  </SearchCardWrapper>
);

export default SearchCard;
