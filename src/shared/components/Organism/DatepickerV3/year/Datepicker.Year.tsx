import dayjs from 'dayjs';
import cnj from '@shared/uikit/utils/cnj';
import type { DateType } from '@shared/types/schedules/schedules';
import CalendarRow from '@shared/components/molecules/CalendarRow/CalendarRow';
import CalendarCell from '@shared/components/molecules/CalendarCell/CalendarCell';
import CalendarView from '@shared/components/molecules/CalendarView/CalendarView';
import type { DatepickerViewTypes } from '@shared/components/Organism/DatepickerV3/types';

import classes from './Datepicker.Year.module.scss';

interface DatepickerYearViewProps {
  year: string[][];
  currentView: DatepickerViewTypes;
  currentDate: string;
  startDate?: string;
  endDate?: string;
  onCellClick?: (date: DateType) => void;
  handleViewChange?: (view: DatepickerViewTypes) => void;
}

export default function YearView({
  year,
  currentDate,
  currentView,
  onCellClick,
  handleViewChange,
  startDate,
  endDate,
}: DatepickerYearViewProps) {
  const isDisabled = (date: string) =>
    (startDate && dayjs(startDate).isAfter(dayjs(date), 'month')) ||
    (endDate && dayjs(endDate).isBefore(dayjs(date), 'month'));

  const isSelected = (date: string) =>
    dayjs(date).isSame(dayjs(currentDate), 'month');

  if (currentView !== 'year') return null;
  return (
    <CalendarView className={classes.yearviewContainer}>
      {year?.map((row, rowIdx) => (
        <CalendarRow
          className={classes.yearviewRow}
          key={`yearly-row-${rowIdx}`}
        >
          {row.map((month, idx) => (
            <CalendarCell
              date={month}
              className={cnj(
                classes.yearviewCell,
                isDisabled(month) && classes.isDisabled,
                isSelected(month) && classes.isSelected
              )}
              typographyProps={{ className: 'my-auto' }}
              key={`month-cell-${rowIdx}-${idx}`}
              onSelect={(date) => {
                onCellClick?.(dayjs(date));
                handleViewChange?.('month');
              }}
              transform={dayjs}
              format="MMMM"
            />
          ))}
        </CalendarRow>
      ))}
    </CalendarView>
  );
}
