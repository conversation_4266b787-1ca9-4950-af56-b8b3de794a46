import dayjs from 'dayjs';

import Flex from '@shared/uikit/Flex';
import type { DateType } from '@shared/types/schedules/schedules';
import SmallTriangle from '@shared/svg/SmallTriangle';
import Button from '@shared/uikit/Button';
import cnj from '@shared/uikit/utils/cnj';
import IconButton from '@shared/uikit/Button/IconButton';
import type { DatepickerViewTypes } from '@shared/components/Organism/DatepickerV3/types';
import { formatStringDate } from '../utils';

import classes from './DatepickerHeader.module.scss';

export interface DatepickerHeaderProps {
  currentDate: string;
  currentView: DatepickerViewTypes;
  disabled?: boolean;
  handleChange?: (date: DateType) => void;
  handleViewChange?: (view: DatepickerViewTypes) => void;
  hideHeader?: boolean;
}

export default function DatepickerHeader({
  currentDate,
  currentView,
  handleChange,
  handleViewChange,
  disabled,
  hideHeader,
}: DatepickerHeaderProps) {
  const onIncrease = () => {
    const newDate =
      currentView !== 'years'
        ? dayjs(currentDate).add(1, currentView)
        : dayjs(currentDate).add(15, 'year');

    handleChange?.(newDate);
  };
  const onDecrease = () => {
    const newDate =
      currentView !== 'years'
        ? dayjs(currentDate).subtract(1, currentView)
        : dayjs(currentDate).subtract(15, 'year');
    handleChange?.(newDate);
  };

  const toggleYearView = () => {
    if (currentView !== 'year') handleViewChange?.('year');
    else handleViewChange?.('month');
  };

  const toggleYearsView = () => {
    if (currentView !== 'years') handleViewChange?.('years');
    else handleViewChange?.('year');
  };
  if (hideHeader) return null;
  return (
    <Flex className={classes.headerContainer}>
      <Flex className={classes.dateInfoContainer}>
        <Button
          schema="gray-semi-transparent"
          labelColor="inherit"
          labelFont="bold"
          onClick={toggleYearView}
          className={cnj(
            classes.topButton,
            classes.monthButton,
            currentView === 'year' && classes.noEvent
          )}
          label={formatStringDate(currentDate, 'MMM')}
        >
          <SmallTriangle
            className={cnj(
              classes.smallTriangle,
              currentView === 'year' && classes.hidden
            )}
          />
        </Button>
        <Button
          schema="gray-semi-transparent"
          labelFont="bold"
          labelColor="inherit"
          onClick={toggleYearsView}
          className={cnj(
            classes.topButton,
            currentView === 'years' && classes.noEvent
          )}
          label={formatStringDate(currentDate, 'YYYY')}
        >
          <SmallTriangle
            className={cnj(
              classes.smallTriangle,
              currentView === 'years' && classes.hidden
            )}
          />
        </Button>
      </Flex>
      <Flex className={classes.actionButtons}>
        <IconButton
          name="chevron-left"
          size="sm"
          colorSchema="transparentSmokeCoal"
          type="fas"
          className={classes.leftChevron}
          disabled={disabled}
          onClick={onDecrease}
        />
        <IconButton
          name="chevron-right"
          size="sm"
          colorSchema="transparentSmokeCoal"
          disabled={disabled}
          type="fas"
          onClick={onIncrease}
        />
      </Flex>
    </Flex>
  );
}
