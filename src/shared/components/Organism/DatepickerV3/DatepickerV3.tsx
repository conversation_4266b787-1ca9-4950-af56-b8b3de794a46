import { useMemo, useState } from 'react';
import dayjs from 'dayjs';
import { weekDaysValueArray } from '@shared/utils/constants/enums/schedulesDb';
import { setDate as setViewDate } from '@shared/stores/schedulesStore';
import type { DateType } from '@shared/types/schedules/schedules';
import type { DatePickerProps } from '@shared/types/components/DatePicker.type';
import cnj from '@shared/uikit/utils/cnj';
import useTranslation from '@shared/utils/hooks/useTranslation';
import Flex from '@shared/uikit/Flex';
import Button from '@shared/uikit/Button';
import type { DatepickerViewTypes } from '@shared/components/Organism/DatepickerV3/types';
import DatepickerHeader from './header/Datepicker.Header';
import {
  getMonthArray,
  getWeekArray,
  getYearArray,
  getYearsArray,
} from './utils';
import MonthView from './month/Datepicker.Month';
import YearView from './year/Datepicker.Year';
import WeekView from './month/Datepicker.Week';
import YearsView from './years/Datepicker.Years';

export default function DatepickerV3({
  data,
  localviewDate,
  classNames,
  doNotShowActivesOfAdjacentMonths,
  onCellClick,
  onDateChange,
  numberOfCells = 7 * 6,
  isWeek,
  hideWeekDayLabels,
  disableDefaultBehaviour,
  showEventsOnClick,
  showEventsOnHover,
  disableSelectionOfPastTime,
  startDate,
  endDate,
  hideHeader,
  hideToday,
}: DatePickerProps) {
  const { t } = useTranslation();
  const [currentView, setCurrentView] = useState<DatepickerViewTypes>('month');

  const [selectedDate, week, month, year, years] = useMemo(() => {
    const current = localviewDate.start.format() as string;
    const weekA = getWeekArray(current);
    const monthA = getMonthArray(current, numberOfCells / 7);
    const yearA = getYearArray(current);
    const yearsA = getYearsArray(current, 5);
    return [current, weekA, monthA, yearA, yearsA];
  }, [localviewDate, numberOfCells]);

  const gotoToday = () => {
    const today = dayjs();
    if (onDateChange) {
      onDateChange(today);
      return;
    }
    if (isDayDisabled(today.format())) return;
    handleCellClick(today);
  };

  const handleSetViewDate = (date: DateType) => {
    if (onDateChange) onDateChange(date);
    else onCellClick?.(date);
  };
  const handleCellClick = (date: DateType) => {
    onCellClick?.(date);
    if (!(disableDefaultBehaviour || showEventsOnClick)) setViewDate(date);
  };

  const showActive = useMemo(() => {
    if (!doNotShowActivesOfAdjacentMonths) return true;
    return localviewDate.start.isSame(data?.viewDate?.start, 'month');
  }, [doNotShowActivesOfAdjacentMonths, localviewDate, data?.viewDate]);

  const isDayDisabled = (date: string, weekdayIndex?: number) => {
    const today = dayjs();
    const currentDate = dayjs(date);
    if (disableSelectionOfPastTime && currentDate.isBefore(today, 'day'))
      return true;
    if (
      weekdayIndex &&
      data?.availableWeekDays?.includes(
        weekDaysValueArray[weekdayIndex - 1]
      ) === false
    )
      return true;
    return false;
  };

  const tstartDate = disableSelectionOfPastTime
    ? dayjs().format()
    : startDate?.format();

  return (
    <Flex className={cnj(classNames?.datePickerContainer)}>
      <DatepickerHeader
        currentDate={selectedDate}
        currentView={currentView}
        handleChange={handleSetViewDate}
        handleViewChange={setCurrentView}
        hideHeader={isWeek || hideHeader}
      />
      <WeekView
        currentDate={selectedDate}
        currentView={currentView}
        onCellClick={handleCellClick}
        week={week}
        handleViewChange={setCurrentView}
        isWeekView={isWeek}
      />
      <MonthView
        currentDate={selectedDate}
        currentView={currentView}
        onCellClick={handleCellClick}
        month={month}
        handleViewChange={setCurrentView}
        isWeekView={isWeek}
        hideWeekDayLabels={hideWeekDayLabels}
        isDisabled={isDayDisabled}
        showActive={showActive}
        cellclassNames={classNames?.datePickerCell}
        showEventsOnClick={showEventsOnClick}
        showEventsOnHover={showEventsOnHover}
      />
      <YearView
        year={year}
        currentView={currentView}
        currentDate={selectedDate}
        onCellClick={handleSetViewDate}
        handleViewChange={setCurrentView}
        startDate={tstartDate}
        endDate={endDate?.format()}
      />
      <YearsView
        years={years}
        currentView={currentView}
        currentDate={selectedDate}
        onCellClick={handleSetViewDate}
        handleViewChange={setCurrentView}
        startDate={tstartDate}
        endDate={endDate?.format()}
      />
      {!hideToday && (
        <Button
          label={t('today_cap')}
          schema="semi-transparent"
          className="mt-8"
          onClick={gotoToday}
        />
      )}
    </Flex>
  );
}
