import dayjs from 'dayjs';

import cnj from '@shared/uikit/utils/cnj';
import type { DateType } from '@shared/types/schedules/schedules';
import CalendarRow from '@shared/components/molecules/CalendarRow/CalendarRow';
import CalendarCell from '@shared/components/molecules/CalendarCell/CalendarCell';
import CalendarView from '@shared/components/molecules/CalendarView/CalendarView';
import type { DatepickerViewTypes } from '@shared/components/Organism/DatepickerV3/types';

import classes from './Datepicker.Years.module.scss';

interface DatepickerYearsViewProps {
  years: string[][];
  currentView: DatepickerViewTypes;
  currentDate: string;
  startDate?: string;
  endDate?: string;
  onCellClick?: (date: DateType) => void;
  handleViewChange?: (view: DatepickerViewTypes) => void;
}

export default function YearsView({
  years,
  currentDate,
  startDate,
  endDate,
  currentView,
  onCellClick,
  handleViewChange,
}: DatepickerYearsViewProps) {
  const isDisabled = (date: string) =>
    (startDate && dayjs(startDate).isAfter(dayjs(date), 'year')) ||
    (endDate && dayjs(endDate).isBefore(dayjs(date), 'year'));

  const isSelected = (date: string) =>
    dayjs(date).isSame(dayjs(currentDate), 'year');

  if (currentView !== 'years') return null;
  return (
    <CalendarView className={classes.yearviewContainer}>
      {years?.map((row, rowIdx) => (
        <CalendarRow
          className={classes.yearviewRow}
          key={`yearly-row-${rowIdx}`}
        >
          {row.map((year, idx) => (
            <CalendarCell
              date={year}
              className={cnj(
                classes.yearviewCell,
                isDisabled(year) && classes.isDisabled,
                isSelected(year) && classes.isSelected
              )}
              key={`month-cell-${rowIdx}-${idx}`}
              onSelect={(date) => {
                onCellClick?.(dayjs(date));
                handleViewChange?.('year');
              }}
              typographyProps={{ className: 'my-auto' }}
              transform={dayjs}
              format="YYYY"
            />
          ))}
        </CalendarRow>
      ))}
    </CalendarView>
  );
}
