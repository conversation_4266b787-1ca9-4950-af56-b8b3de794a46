import { useCallback } from 'react';
import type { DateType } from '@shared/types/schedules/schedules';
import dayjs from 'dayjs';
import cnj from '@shared/uikit/utils/cnj';
import CalendarRow from '@shared/components/molecules/CalendarRow/CalendarRow';
import CalendarCell from '@shared/components/molecules/CalendarCell/CalendarCell';
import CalendarView from '@shared/components/molecules/CalendarView/CalendarView';
import type { DatepickerViewTypes } from '@shared/components/Organism/DatepickerV3/types';
import { EventsListPopper } from '@shared/components/molecules/EventsListPopover/EventsListPopper';
import classes from './Datepicker.Month.module.scss';

const weekendDays = [1, 7];
export interface DatepickerMonthViewProps {
  currentView: DatepickerViewTypes;
  currentDate: string;
  month: string[][];
  onCellClick?: (date: DateType) => void;
  handleViewChange?: (view: DatepickerViewTypes) => void;
  isDisabled?: (date: string, weekdayIndex?: number) => boolean;
  isWeekView?: boolean;
  hideWeekDayLabels?: boolean;
  showActive?: boolean;
  cellclassNames?: string;
  showEventsOnClick?: boolean;
  showEventsOnHover?: boolean;
}

export default function MonthView({
  currentDate,
  currentView,
  month,
  onCellClick,
  handleViewChange,
  isDisabled,
  isWeekView,
  hideWeekDayLabels,
  showActive,
  cellclassNames,
  showEventsOnClick,
  showEventsOnHover,
}: DatepickerMonthViewProps) {
  const isInMonth = useCallback(
    (date: string) => dayjs(currentDate).isSame(dayjs(date), 'month'),
    [currentDate]
  );
  const isToday = (date: string) => dayjs(date).isSame(dayjs(), 'day');

  const isSelected = (date: string) =>
    showActive && dayjs(date).isSame(dayjs(currentDate), 'day');
  const isWeekend = (dayOfWeek: number) => weekendDays.includes(dayOfWeek);

  const handleSelect = (date: string) => {
    onCellClick?.(dayjs(date));
    if (isWeekView) handleViewChange?.('week');
  };

  if (currentView !== 'month') return null;
  return (
    <>
      {hideWeekDayLabels ? null : (
        <CalendarRow className={classes.monthlyCellsHeader}>
          {month[0].map((day, index) => (
            <CalendarCell
              key={`weekday-label-${index}`}
              date={day}
              className={cnj(classes.weekdayLabelCell)}
              transform={dayjs}
              format="ddd"
            />
          ))}
        </CalendarRow>
      )}
      <CalendarView className={classes.monthCell}>
        {month.map((week, index) => (
          <CalendarRow className={classes.weekRow} key={`week-row-${index}`}>
            {week?.map((date, idx) => (
              <EventsListPopper
                key={`day-cell-${index}-${idx}`}
                disabled={!(showEventsOnClick || showEventsOnHover)}
                date={dayjs(date)}
                applySelectedFilters
                popperMenuProps={{
                  showWithHover: showEventsOnHover,
                }}
              >
                <CalendarCell
                  key={`day-cell-${index}-${idx}`}
                  date={date}
                  transform={dayjs}
                  className={cnj(
                    classes.dayCell,
                    classes.activeDayCell,
                    cellclassNames,
                    isToday(date) && classes.isToday,
                    !isInMonth(date) && classes.isOutMonth,
                    isWeekend(idx + 1) && classes.isWeekend,
                    isDisabled?.(date, idx + 1) && classes.isDisabled,
                    isSelected(date) && classes.isSelected
                  )}
                  typographyProps={{ className: 'my-auto' }}
                  onSelect={handleSelect}
                  selectedDate={currentDate}
                  format="D"
                />
              </EventsListPopper>
            ))}
          </CalendarRow>
        ))}
      </CalendarView>
    </>
  );
}
