import { useCallback } from 'react';
import type { DateType } from '@shared/types/schedules/schedules';
import dayjs from 'dayjs';
import cnj from '@shared/uikit/utils/cnj';
import CalendarRow from '@shared/components/molecules/CalendarRow/CalendarRow';
import CalendarCell from '@shared/components/molecules/CalendarCell/CalendarCell';
import CalendarView from '@shared/components/molecules/CalendarView/CalendarView';
import type { DatepickerViewTypes } from '@shared/components/Organism/DatepickerV3/types';

import classes from './Datepicker.Month.module.scss';

const weekendDays = [1, 7];
export interface DatepickerWeekViewProps {
  currentView: DatepickerViewTypes;
  currentDate: string;
  week: string[];
  isWeekView?: boolean;
  onCellClick?: (date: DateType) => void;
  handleViewChange?: (view: DatepickerViewTypes) => void;
}

export default function WeekView({
  currentDate,
  currentView,
  week,
  isWeekView,
  onCellClick,
  handleViewChange,
}: DatepickerWeekViewProps) {
  const isInMonth = useCallback(
    (date: string) => dayjs(currentDate).isSame(dayjs(date), 'month'),
    [currentDate]
  );
  const isToday = (date: string) => dayjs(date).isSame(dayjs(), 'day');
  const isWeekend = (dayOfWeek: number) => weekendDays.includes(dayOfWeek);

  const handleSelect = (date: string) => {
    onCellClick?.(dayjs(date));
  };
  if (currentView !== 'week') return null;
  return (
    <>
      <CalendarRow className={classes.monthlyCellsHeader}>
        {week.map((day, index) => (
          <CalendarCell
            key={`weekday-label-${index}`}
            date={day}
            className={cnj(classes.weekdayLabelCell)}
            transform={dayjs}
            format="ddd"
          />
        ))}
      </CalendarRow>
      <CalendarView className={classes.monthCell}>
        <CalendarRow className={classes.weekRow}>
          {week?.map((date, idx) => (
            <CalendarCell
              key={`day-cell-${idx}`}
              date={date}
              transform={dayjs}
              className={cnj(
                classes.dayCell,
                classes.activeDayCell,
                isToday(date) && classes.isToday,
                !isInMonth(date) && classes.isOutMonth,
                isWeekend(idx + 1) && classes.isWeekend
              )}
              onSelect={handleSelect}
              selectedDate={currentDate}
              format="D"
            />
          ))}
        </CalendarRow>
      </CalendarView>
    </>
  );
}
