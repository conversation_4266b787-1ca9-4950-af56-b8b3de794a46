'use client';

import React, { useEffect } from 'react';
import { isBusinessApp } from 'shared/utils/getAppEnv';
import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryStreamedHydration } from '@tanstack/react-query-next-experimental';
import { ThemeProvider } from 'shared/uikit/ThemeProvider';
import { GlobalProvider } from 'shared/contexts/Global/global.provider';
import { AuthProvider } from 'shared/contexts/Auth/auth.provider';
import { LanguageProvider } from 'shared/contexts/Language';
import QueryKeys from 'shared/utils/constants/queryKeys';
import Profiler from 'shared/uikit/Optimization/Profiler';
import { SchedulesProvider } from 'shared/providers/SchedulesProvider';
import ConfirmProvider from 'shared/uikit/Confirmation/confirm.provider';
import dynamic from 'next/dynamic';
import { Workbox } from 'workbox-window';
import useGetQueryClient from '@shared/utils/hooks/useGetQueryClient';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import isDev from '@shared/utils/constants/isDev';
import { BackToLastPage } from './BackToLastPageProvider';
import LocalizatoinProvider from './LocalizationProvider';

const InstallAppPopUp = dynamic(
  () => import('shared/components/molecules/InstallPopUp'),
  { ssr: false }
);
const ToastContainer = dynamic(() => import('shared/uikit/ToastContainer'), {
  ssr: false,
});

const CookiesPopup = dynamic(
  () => import('shared/components/molecules/CookiesPopup'),
  { ssr: false }
);
const Modals = dynamic(() => import('./Modals'), { ssr: false });

const Observer = dynamic(() => import('./Observers'), { ssr: false });

interface Props {
  authUser: any;
  businessPage: any;
  isDark: boolean;
  lng: string;
}

function Providers({
  authUser,
  businessPage,
  isDark,
  lng,
  children,
}: React.PropsWithChildren<Props>) {
  const [queryClient] = React.useState(useGetQueryClient());

  if (authUser) {
    queryClient.setQueryData([QueryKeys.authUser], authUser);
  }
  if (businessPage) {
    queryClient.setQueryData([QueryKeys.businessPage], businessPage);
  }

  useEffect(() => {
    if (!('serviceWorker' in navigator) || isDev) {
      console.warn('Pwa support is disabled');
      return;
    }

    const wb = new Workbox('sw.js');
    wb.register();
  }, []);

  return (
    <Profiler id="app-root">
      <LanguageProvider lng={lng}>
        <LocalizatoinProvider locale={lng}>
          <GlobalProvider initialValue={{ isBusinessApp }}>
            <AuthProvider initialValue={{ isLoggedIn: !!authUser }}>
              <QueryClientProvider client={queryClient}>
                <ReactQueryStreamedHydration>
                  <ThemeProvider isDark={isDark}>
                    <ConfirmProvider>
                      {/* <SchedulesProvider> */}
                      {children}
                      <Modals />
                      <InstallAppPopUp />
                      <ToastContainer />
                      <CookiesPopup />
                      <BackToLastPage />
                      {/* </SchedulesProvider> */}
                    </ConfirmProvider>
                  </ThemeProvider>
                  <Observer />
                </ReactQueryStreamedHydration>
                <ReactQueryDevtools initialIsOpen={false} />
              </QueryClientProvider>
            </AuthProvider>
          </GlobalProvider>
        </LocalizatoinProvider>
      </LanguageProvider>
    </Profiler>
  );
}

export default Providers;
