import formatDate from 'shared/utils/toolkit/formatDate';
import datesDuration from 'shared/utils/toolkit/datesDuration';
import { db, jobsDb } from 'shared/utils/constants/enums';
import collectionToObjectByKey from 'shared/utils/toolkit/collectionToObjectByKey';
import { DESCRIPTION_MAX_LENGTH } from 'shared/constants/enums';
import type { Experience } from 'shared/types/experience';
import type { AdvanceCardProps } from '@shared/components/molecules/AdvanceCard/AdvanceCard.component';
import {
  calculateDatesSum,
  displayCumulativeDate,
  displayDuration,
} from '../../app/(lobox)/(profile)/[username]/(about)/partials/utils';

export const addDisplayDuration = <DataType extends NormalizedExperience>(
  item: DataType
) =>
  'steps' in item
    ? {
        ...item,
        secondText: displayDuration(calculateDatesSum(item.steps)),
      }
    : item;

export const addDisplayDurationWithAccRange = <
  DataType extends NormalizedExperience,
>(
  item: DataType,
  t = (k: string) => k
) =>
  item.steps
    ? {
        ...item,
        secondText: displayCumulativeDate(item.steps, t),
        secondTextHelper: displayDuration(calculateDatesSum(item.steps)),
      }
    : item;

export const findEmploymentLabelByType = (value: string | number) => {
  const employment = db.EMPLOYMENT_TYPES.find((item) => item.value === value);
  return employment?.label;
};

export function safeCutDescription(expr: Experience): Experience {
  let desc: string = expr.description;
  if (!desc || desc.length < DESCRIPTION_MAX_LENGTH) return expr;
  let safeDes = '';
  while (safeDes.length < DESCRIPTION_MAX_LENGTH && desc.length > 0) {
    // find next punctuation: any of (; ! ? .)
    const match = desc.match(/[;!?\.]/);
    if (match?.index === undefined) break;
    // take next sentence
    const next = desc.substring(0, match.index + 1);
    // take rest:
    desc = desc.substring(match.index + 1);
    // add next sentence to safe description with extra check
    if (safeDes.length + next.length > DESCRIPTION_MAX_LENGTH) break;
    safeDes += next;
  }
  return {
    ...expr,
    description: safeDes,
  };
}

export const convertExperienceApiToForm = (curr: Experience) => {
  const {
    id,
    companyName,
    occupationName,
    occupationLookupId,
    companyPageId,
    description,
    volunteer,
    currentlyWorking,
    startDate,
    endDate,
    employmentType,
    originalId,
    location,
    pageCroppedImageUrl,
    workPlaceType,
    cause,
  } = curr;

  return {
    location: location
      ? {
          ...location,
          label: location.title,
          value: location.externalId,
        }
      : undefined,
    id,
    job: {
      label: occupationName,
      value: occupationLookupId,
      public: false,
    },
    company: {
      label: companyName,
      value: companyPageId,
      public: false,
      image: pageCroppedImageUrl,
    },
    cause: cause ? collectionToObjectByKey(db.CAUSE_OPTIONS)[cause] : undefined,
    employmentType: {
      value: employmentType,
      label: findEmploymentLabelByType(employmentType),
    },
    workPlaceType: workPlaceType
      ? collectionToObjectByKey(jobsDb.WORK_SPACE_MODEL)[workPlaceType]
      : undefined,
    description,
    volunteer,
    originalId,
    currentlyWorking,
    startDate,
    endDate,
  };
};

export const experienceNormalizer = (
  prev: Array<NormalizedExperience>,
  curr: Experience
): NormalizedExperience[] => {
  const {
    id,
    companyName,
    occupationName,
    companyPageId,
    description,
    startDate,
    endDate,
    pageCroppedImageUrl,
    location,
    employmentType,
    cause,
    workPlaceTypeLabel,
  } = curr;
  const realData = convertExperienceApiToForm(curr);
  const formattedEndDate = endDate
    ? formatDate(endDate, 'MMM YYYY')
    : 'Present';
  const formattedStartDate = formatDate(startDate, 'MMM YYYY');
  const durationObj = datesDuration({
    startDate,
    endDate: endDate || new Date().toISOString(),
  });
  const duration = durationObj ? displayDuration(durationObj) : '';
  const company = prev.find((i) => i.companyPageId === companyPageId);
  const objectId = !companyPageId?.includes?.('_temp')
    ? companyPageId
    : undefined;
  const isPageAnonymous = false;
  //  !objectId && !!companyPageId;

  const textDotHelper = [
    workPlaceTypeLabel,
    location ? location?.title : undefined,
  ].filter(Boolean) as string[];

  if (company) {
    return prev.map((i) => {
      if (i.companyPageId === companyPageId) {
        return {
          id: companyPageId,
          companyPageId,
          image: pageCroppedImageUrl,
          firstText: companyName,
          firstTextAdditionalProps: { objectId, isPageAnonymous },
          objectId,
          steps: [
            ...(company?.steps || [
              {
                id: i.id,
                firstText: i?.firstText,
                firstTextDotHelper: i.fourthTextDotHelper,
                secondText: i.secondText,
                secondTextHelper: i.secondTextHelper,
                secondTextProps: {
                  isWordWrap: false,
                },
                secondTextSecondHelper: i.secondTextSecondHelper || i?.cause,
                longText: i.longText,
                realData: i.realData,
                durationObj: i.durationObj,
              },
            ]),
            {
              id,
              firstText: occupationName,
              firstTextDotHelper: textDotHelper,
              secondText:
                formattedStartDate === formattedEndDate
                  ? formattedEndDate
                  : `${formattedStartDate} - ${formattedEndDate}`,
              secondTextHelper: duration,
              secondTextProps: {
                isWordWrap: false,
              },
              secondTextSecondHelper:
                findEmploymentLabelByType(employmentType) || cause,
              longText: description,
              realData,
              durationObj,
            },
          ],
        };
      }
      return i;
    });
  }
  return [
    ...prev,
    {
      id,
      companyPageId,
      image: pageCroppedImageUrl,
      firstText: occupationName,
      secondText: `${formattedStartDate} - ${formattedEndDate}`,
      secondTextHelper: duration,
      secondTextSecondHelper:
        findEmploymentLabelByType(employmentType) || cause,
      secondTextProps: {
        isWordWrap: false,
      },
      secondTextSecondHelperProps: {
        isTruncated: true,
      },
      fourthText: companyName,
      fourthTextDotHelper: textDotHelper,
      fourthTextAdditionalProps: { objectId, isPageAnonymous },
      longText: description,
      realData,
      durationObj,
      objectId,
    },
  ];
};

export type NormalizedExperience = AdvanceCardProps['data'] &
  Partial<Experience> & {
    companyPageId?: string;
    realData?: ReturnType<typeof convertExperienceApiToForm>;
    durationObj?: Duration;
    steps?: NormalizedExperience[];
  };
