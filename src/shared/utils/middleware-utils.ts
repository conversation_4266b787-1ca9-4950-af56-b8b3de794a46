import {
  doesNotNeedAuthPages,
  forbiddenForAuthUsersPages,
  needsAuthPages,
} from 'shared/utils/constants/routeNames';
import type { UserType } from 'shared/types/user';
import type { PageType } from 'shared/types/page';

export const isNeedAuthPage = (pathname: string) =>
  needsAuthPages.some((x) =>
    pathname.split('/').includes(x.replace('/', ''))
  ) && !doesNotNeedAuthPages?.some?.((x: string) => pathname.includes(x));

export const isForbiddenForAuthUsersPages = (pathname: string) =>
  forbiddenForAuthUsersPages.some((x) => x === pathname);

export const businessPageNormalizer = (page: PageType) => {
  const myMemberships = page?.myMemberships?.reduce(
    (prev, curr) =>
      curr.status === 'ACCEPTED'
        ? [...prev, { status: curr.status, role: curr.role }]
        : prev,
    []
  );

  return {
    id: page?.id,
    croppedImageUrl: page?.croppedImageUrl,
    title: page?.title,
    username: page?.username,
    type: 'PAGE',
    status: page?.status,
    ownerId: page?.ownerId,
    myMemberships,
    isMember: !!myMemberships?.length,
    location: {
      countryCode: page?.location?.countryCode,
    },
  };
};

export const authUserNormalizer = (authUser: UserType) => ({
  id: authUser?.id,
  croppedImageUrl: authUser?.croppedImageUrl,
  fullName: authUser?.fullName,
  username: authUser?.username,
  type: 'PERSON',
  location: {
    countryCode: authUser?.location?.countryCode,
  },
});

export const encodeObject = <T>(inputObject: T): string => {
  const jsonString = JSON.stringify(inputObject);
  return encodeURIComponent(jsonString);
};

export const decodeObject = <T>(encodedString: string = '{}'): T => {
  try {
    const decodedString = decodeURIComponent(encodedString);
    return JSON.parse(decodedString) as T;
  } catch (error) {
    console.warn(error);
    return undefined as T;
  }
};
