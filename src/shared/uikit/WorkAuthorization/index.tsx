import type { GrayContainerProps } from '@shared/components/atoms/containers/GrayContainer';
import GrayContainer from '@shared/components/atoms/containers/GrayContainer';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { Endpoints, geoEndpoints } from '@shared/utils/constants';
import geoNormalizer from '@shared/utils/normalizers/geo';
import type { WorkAuthorizationValueProps } from '@shared/types/jobsProps';
import AsyncAutoComplete from '../AutoComplete/AsyncAutoComplete';
import AsyncAutoCompleteWithExtraParams from '../AutoComplete/AsyncAutoCompleteWithExtraParams';

const WorkAuthorization = (
  props: GrayContainerProps & {
    value: WorkAuthorizationValueProps;
    onChange: (value: WorkAuthorizationValueProps) => void;
    index: number;
    list: WorkAuthorizationValueProps[];
    onRemove: (index: number) => void;
    required?: boolean;
  }
) => {
  const {
    value,
    onChange,
    index,
    list,
    required = false,
    onRemove,
    ...rest
  } = props;
  const { t } = useTranslation();

  const normalizer = (data: any) =>
    data?.reduce((acc: any[], curr: { id: string; title: string }) => {
      if (list.some(({ authorization: item }) => item?.value === curr.id))
        return acc;
      return [
        ...acc,
        {
          label: curr.title,
          name: curr.title,
          value: curr.id,
        },
      ];
    }, []);

  return (
    <GrayContainer
      {...rest}
      onRemove={() => onRemove(index)}
      canDuplicate={!!value.country?.value && !!value.authorization?.value}
    >
      <AsyncAutoCompleteWithExtraParams
        name="wa_location"
        url={geoEndpoints.location.searchLocation}
        rightIconProps={{
          name: 'search',
        }}
        normalizer={geoNormalizer.searchLocation}
        limit={10}
        returnEmpty={false}
        visibleRightIcon
        label={`${t('location')} ${!required ? t('(optional)') : ''}`}
        onChange={(country: { value: string; label: string }) => {
          onChange({ ...value, country });
        }}
        value={value.country}
      />
      <AsyncAutoComplete
        name="wa_title"
        url={Endpoints.App.Common.searchAuthorization}
        rightIconProps={{
          name: 'search',
        }}
        normalizer={normalizer}
        limit={10}
        returnEmpty={false}
        visibleRightIcon
        label={`${t('authorization_type')} ${!required ? t('(optional)') : ''}`}
        disabled={!value.country}
        params={{ countryCode: value.country?.value }}
        onChange={(authorization: { value: string; label: string }) => {
          onChange({ ...value, authorization });
        }}
        value={value.authorization}
      />
    </GrayContainer>
  );
};

export default WorkAuthorization;
