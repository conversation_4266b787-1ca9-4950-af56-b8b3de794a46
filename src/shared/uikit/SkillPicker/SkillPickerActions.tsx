import type { MenuItem } from '@shared/types/components/Menu.type';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Menu from '@shared/components/molecules/Menu/Menu';

interface SkillPickerActionsProps {
  onEdit: () => void;
  onDelete: () => void;
}

const SkillPickerActions = ({ onEdit, onDelete }: SkillPickerActionsProps) => {
  const { t } = useTranslation();
  const skillMenuActions: MenuItem[] = [
    {
      iconName: 'pen',
      label: t('edit'),
      onClick: onEdit,
    },
    {
      iconName: 'trash',
      label: t('delete'),
      onClick: onDelete,
    },
  ];
  return (
    <Menu
      menuItems={skillMenuActions}
      menuPlacement="bottom-end"
      menuItemSize={20}
      menuIconSize="sm"
      className="!absolute top-0 right-0"
      classNames={{ itemIconWrapper: '!m-0', menu: '!p-0' }}
    />
  );
};

export default SkillPickerActions;
