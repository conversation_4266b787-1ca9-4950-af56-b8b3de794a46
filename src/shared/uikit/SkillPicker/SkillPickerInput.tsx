import React, { useState } from 'react';
import Endpoints from 'shared/utils/constants/endpoints';
import useTranslation from 'shared/utils/hooks/useTranslation';
import trim from 'lodash/trim';
import AsyncAutoComplete from 'shared/uikit/AutoComplete/AsyncAutoComplete';
import skillsResponseNormalizer from 'shared/utils/normalizers/skillsResponseNormalizer';
import Flex from '../Flex';
import type { StateSkill } from './types';
import IconButton from '../Button/IconButton';

interface SkillPickerInputsProps {
  onConfirm?: ({ name, level }: any) => void;
  skillInputValues: StateSkill[];
  editingMode?: boolean;
  canClose?: boolean;
  selectedSkills?: any[];
  onDiscard?: () => void;
  handleChange?: (value: { label: string; value: string }) => void;
  isLoading?: boolean;
}

const SingleSkillPickerInput = ({
  onConfirm,
  skillInputValues = [],
  editingMode,
  canClose = true,
  selectedSkills,
  handleChange,
  onDiscard,
  isLoading,
}: SkillPickerInputsProps): JSX.Element => {
  const { t } = useTranslation();
  const [hardRefetch, setHardRefetch] = useState(false);

  const confirmSkill = (skill: { name: string; level: string }, id: string) => {
    onConfirm?.(skill, id);
    if (selectedSkills) {
      setHardRefetch(true);
    }
  };

  const onChangeSkill = (value: { label: string; value: string }) => {
    handleChange?.(value);
  };

  return (
    <>
      {skillInputValues?.map((skill: any) => {
        const isValidLabel = trim(skill.name)?.length;
        return (
          <Flex
            flexDir="row"
            className="w-full relative"
            alignItems="center"
            key={skill.id}
          >
            <AsyncAutoComplete
              variant="simple-large"
              name="skillName"
              className="w-full"
              placeholder={t('select_skill')}
              rightIconProps={{ name: 'chevron-down', size: 'md18' }}
              visibleRightIcon
              value={skill.name}
              onChange={onChangeSkill}
              clearable
              url={Endpoints.App.Common.getSkills}
              editable={editingMode}
              displayName={skill.name}
              initSearchValue={skill.name}
              normalizer={(res) => {
                setHardRefetch(false);
                return skillsResponseNormalizer(res, selectedSkills);
              }}
              hardRefetch={hardRefetch}
            />

            <Flex
              alignItems="center"
              className="gap-8 justify-between pl-8"
              flexDir="row"
            >
              <IconButton
                name="times"
                size="sm20"
                onClick={onDiscard}
                disabled={!canClose || isLoading}
              />
              <IconButton
                name="check"
                size="sm20"
                disabled={!isValidLabel || isLoading}
                onClick={() =>
                  confirmSkill(
                    { name: skill.name, level: skill.level },
                    skill.id
                  )
                }
              />
            </Flex>
          </Flex>
        );
      })}
    </>
  );
};

export default SingleSkillPickerInput;
