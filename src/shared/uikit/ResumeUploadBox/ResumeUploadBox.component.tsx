import React, { useRef, useState } from 'react';
import jobsApi from 'shared/utils/api/jobs';
import preventClickHandler from 'shared/utils/toolkit/preventClickHandler';
import useReactMutation from 'shared/utils/hooks/useReactMutation';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { UploadedResumeCard } from '@shared/components/Organism/UploadedResumeSection/UploadedResumeCard.component';
import ParseTextStringCP from 'shared/components/molecules/TranslateReplacer';
import Info from '@shared/components/molecules/Info/Info';
import { ShareEntities, ShareEntityTab } from '@shared/types/share/entities';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';
import callbacksExecutor from '@shared/utils/callbacksExecutor';
import BaseButton from '@shared/uikit/Button/BaseButton';
import ResumePlusIcon from '@shared/svg/ResumePlusIcon';
import classes from './ResumeUploadBox.component.module.scss';
import Flex from '../Flex';
import cnj from '../utils/cnj';
import FilePicker from '../FilePicker';
import Typography from '../Typography';
import Dropzone from '../FilePicker/dropzone';
import useMedia from '../utils/useMedia';
import ResumePickerUploading from './ResumeUploadBox.uploading';
import ResumePickerPreview from './ResumeUploadBox.preview';
import useOpenConfirm from '../Confirmation/useOpenConfirm';

interface IResumeValue {
  createdDate?: Date;
  id?: string;
  name?: string;
  link?: string;
  url?: string;
  originalFileName?: string;
}

interface ResumePickerProps {
  value: IResumeValue;
  onChange: (value: IResumeValue) => void;
  className?: string;
  classNames?: {
    root?: string;
    fileContainer?: string;
    addBtn?: string;
    info?: string;
    icon?: string;
  };
  isPreview: boolean;
  labels: { [key: string]: string };
  isAjaxCall?: boolean;
  onSuccessDelete: () => void;
  isUploading: boolean;
  onFilePickedHandler: () => void;
  ownerName: string;
  visibleShare?: boolean;
}

const ResumeUploadBox = ({
  value = {},
  onChange,
  className,
  classNames = {},
  onFilePickedHandler,
  isPreview,
  labels = {},
  isAjaxCall = true,
  onSuccessDelete,
  isUploading,
  ownerName,
  visibleShare = true,
}: ResumePickerProps) => {
  const [progress, setProgress] = useState(0);
  const [isDragEntered, toggleIsDragEntered] = useState(false);
  const { t } = useTranslation();
  const { openConfirmDialog } = useOpenConfirm({
    variant: 'wideRightSideModal',
  });
  const { isMoreThanTablet } = useMedia();
  const appDispatch = useGlobalDispatch();

  const { mutate: deleteResumeApi } = useReactMutation({
    apiFunc: jobsApi.deleteResume,
    onSuccess: onSuccessDelete,
  });
  const pickerRef = useRef<{ click: Function }>(null);
  const empty = !isUploading && !value?.id;

  const openPicker = (e?: any) => {
    preventClickHandler(e);
    pickerRef.current?.click();
  };

  const removeFile = () => {
    openConfirmDialog({
      title: t('delete_resume'),
      message: t('r_y_s_y_w_t_del_resume'),
      confirmButtonText: t('delete'),
      cancelButtonText: t('cancel'),
      isAjaxCall,
      apiProps: {
        func: deleteResumeApi,
        variables: { id: value?.id },
      },
      confirmCallback: () => onChange({}),
    });
  };
  const onDragEnter = () => toggleIsDragEntered(true);
  const onDragLeave = () => toggleIsDragEntered(false);
  const canselUpload = () => {
    console.log('cancel');
  };
  const openShareViaMessage = () => {
    appDispatch({
      type: 'SET_SHARE_ENTITY_TABBED_MODAL_DATA',
      payload: {
        isOpen: true,
        tabs: [
          ShareEntityTab.COPY_LINK,
          ShareEntityTab.SHARE_VIA_MESSAGE,
          ShareEntityTab.SHARE_VIA_EMAIL,
        ],
        entityData: {
          attachment: {
            type: ShareEntities.RESUME,
            data: {
              ...value,
              type: 'resume',
              link: value?.link ?? value?.url,
              fullName: ownerName,
            },
          },
        },
      },
    });
  };

  return (
    <Flex
      className={cnj(
        classes.resumeContainer,
        isDragEntered && classes.isDragEntered,
        empty && classes.resumeContainer_hover,
        classNames?.root,
        className
      )}
    >
      {!isPreview && empty && (
        <BaseButton className={classes.baseButton} onClick={openPicker}>
          <Dropzone
            onFilePicked={callbacksExecutor(onFilePickedHandler, onDragLeave)}
            onDragEnter={onDragEnter}
            onDragLeave={onDragLeave}
            accept={{
              'application/pdf': ['.doc', '.docx', '.txt', '.pdf'],
            }}
            classes={{
              dropzone: classes.dropzone,
              container: cnj(classes.container),
            }}
          >
            <Flex className={cnj(classes.addBtn, classNames?.addBtn)}>
              <Flex className={cnj(classes.iconWrapper, classNames?.icon)}>
                <ResumePlusIcon />
              </Flex>
              {isMoreThanTablet ? (
                <ParseTextStringCP
                  textProps={{ font: '500', className: 'text-center' }}
                  textString={t('drg_drp_resume_title')}
                  tagComponentMap={{
                    0: (text) => (
                      <Typography
                        color="brand"
                        className="underline"
                        onClick={openPicker}
                        height={16}
                      >
                        {t(text)}
                      </Typography>
                    ),
                  }}
                />
              ) : (
                <Typography
                  onClick={openPicker}
                  font="500"
                  size={14}
                  height={16}
                  color="brand"
                  className="underline"
                >
                  {t('tap_t_u_r')}
                </Typography>
              )}
              <Typography
                size={12}
                color="primaryDisabledText"
                height={14}
                textAlign="center"
                mt={8}
              >
                {t('file_types_accept')}
              </Typography>
              <Info
                icon="info-circle"
                text={t('uploading_linkedin_resume_results_in_100_accuracy')}
                color="secondaryDisabledText"
                textColor="secondaryDisabledText"
                className={cnj(classes.info, classNames?.info)}
                textProps={{ size: 12, height: 18 }}
              />
            </Flex>
          </Dropzone>
        </BaseButton>
      )}

      {value?.id && !isUploading && !isPreview && (
        <Flex className="flex-1 justify-center  items-center">
          <UploadedResumeCard
            name={ownerName}
            classNames={{
              wrapper: 'w-full',
            }}
            resumeLink={value?.link ?? value?.url}
            showControls
            onDeleteClick={removeFile}
            onUploadClick={openPicker}
            onShareClick={visibleShare ? openShareViaMessage : undefined}
          />
        </Flex>
      )}
      {isUploading && (
        <ResumePickerUploading
          progress={progress}
          label={labels.uploading}
          removeFile={canselUpload}
        />
      )}
      {isPreview && (
        <ResumePickerPreview
          label={(value?.originalFileName ?? value.name) || ''}
        />
      )}
      {!isPreview && (
        <FilePicker
          fileSize={20 * 1024 * 1024}
          ref={pickerRef}
          onFilePicked={callbacksExecutor(onFilePickedHandler, onDragLeave)}
          type="resume"
        />
      )}
    </Flex>
  );
};

export default ResumeUploadBox;
