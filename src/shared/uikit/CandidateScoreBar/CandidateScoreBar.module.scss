@import '/src/shared/theme/theme.scss';

.barWrapper {
  position: relative;
  height: 20px;
  border-radius: variables(gutter) * 0.25;
  background: transparent;
}

.bar {
  width: 100%;
  height: 100%;
  border-radius: variables(gutter) * 0.25;
  background: linear-gradient(to right, #d32f2f, #f57c00, #388e3c);
}

.scoreBubble {
  position: absolute;
  top: 0;
  bottom: 0;
  background-color: colors(popOverBg_white);
  color: white;
  padding: 2px 8px;
  white-space: nowrap;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  height: 20px;
}
