import React from 'react';
import useTranslation from '@shared/utils/hooks/useTranslation';
import styles from './CandidateScoreBar.module.scss';
import Typography from '../Typography';
import Flex from '../Flex';

type CandidateScoreBarProps = {
  score: number; // must be between 0 and 100
  showTitle?: boolean;
};

const CandidateScoreBar: React.FC<CandidateScoreBarProps> = ({
  score,
  showTitle = true,
}) => {
  const { t } = useTranslation();
  const clampedScore = Math.max(0, Math.min(100, score));
  const position = `${clampedScore}%`;

  return (
    <Flex className={styles.container}>
      <Typography
        className="mb-8"
        color="colorIconForth2"
        size={14}
        height={14}
      >
        {showTitle && t('candidate_score_vs_job')}
      </Typography>
      <Flex className={styles.barWrapper}>
        <Flex className={styles.bar} />
        <Flex
          className={styles.scoreBubble}
          style={{ left: position, transform: 'translateX(-50%)' }}
        >
          <Typography height={15} font="bold" color="smoke_coal">
            {clampedScore}%
          </Typography>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default CandidateScoreBar;
