'use client';

import { createRoot } from 'react-dom/client';
import React, {
  type ReactElement,
  type ReactNode,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { flushSync } from 'react-dom';
import type Quill from 'quill';
import Delta from 'quill-delta';
import { type MagicUrlOptions } from 'quill-magic-url';
import useCssVariables from 'shared/hooks/useCssVariables';
import removeHtmlTagsInstring from 'shared/utils/toolkit/removeHtmlTagsInstring';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { debounceAsync } from 'shared/utils/toolkit/debounceAsync';
import { focusAndScroll } from 'shared/utils/toolkit/focusAndScroll';
import EmojiPickerButton from 'shared/uikit/EmojiPickerButton';
import { useResizeObserver } from 'shared/hooks/useResizeObserver';
import isEmpty from 'shared/utils/toolkit/isEmpty';
import { MODAL_FOOTER_CLASSNAME } from 'shared/constants/enums';
import cnj from '../utils/cnj';
import useTheme from '../utils/useTheme';
import type { TextProps } from '../Typography';
import Typography from '../Typography';
import Icon from '../Icon';
import { useQuill } from './RichText.useQuill';
import HashtagItem from './hashtag.component';
import UserItem from './user.component';
import DynamicTemplateItem from './DynamicTemplate.component';
import { getHashtags, getPopularHashtags, getUsers } from './richTextApi';
import Flex from '../Flex';
import FixedRightSideModal from '../Modal/FixedRightSideModalDialog';
import ModalBody from '../Modal/ModalBody';
import ModalFooter from '../Modal/ModalFooter';
import ModalHeaderSimple from '../Modal/ModalHeaderSimple';
import Button from '../Button';
import useMedia from '../utils/useMedia';
import classes from './RichText.component.module.scss';
import 'quill-mention/dist/quill.mention.css';
import 'quill/dist/quill.bubble.css';

const initialValue = '<p><br></p>';

export type RichTextRefType = {
  focus: () => void;
  reset: () => void;
  quill: Quill | undefined;
  onChooseEmoji: (value: string) => void;
};

export type RichTextProps = {
  label?: string;
  placeholderFontSize?: string;
  value?: string;
  defaultValue?: string;
  variant?: 'form-input' | 'comment-input' | 'simple';
  readOnly?: boolean;
  showEmoji?: boolean;
  onChange?: (text: string) => void;
  onEnterKeyDown?: (text: string) => void;
  helperText?: string;
  isEnabledMention?: boolean;
  isEnabledHashtags?: boolean;
  disableNewLineWithEnter?: boolean;
  changeWithDebounce?: boolean;
  replyMention?: {
    id: string;
    username: string;
    fullName?: string;
  };
  defaultHashtag?: boolean;
  fixMentionDropdown?: boolean;
  mentionDropDownPosition: 'top' | 'bottom';
  [key: string]: any;
  emojiClassName?: string;
  spaceFromEmoji?: boolean;
  maxLength?: number;
  labelProps?: TextProps<'span'>;
  noBorder?: boolean;
  emojiProps?: any;
  isModalView?: boolean;
  isInFooter?: boolean;
  magicUrl?: boolean | MagicUrlOptions;
  noLeft?: boolean;
  additionalComponent?: ReactElement;
  emojiPickerClassName?: string;
  templateTexts?: string[];
  disabled?: boolean;
  disabledReadOnly?: boolean;
  topComponents?: ReactNode;
  dynamicTemplates: { label: string; value: string }[];
  singleLine?: boolean;
  ref?: React.Ref<HTMLImageElement>;
};

const RichText = (props: RichTextProps): JSX.Element => {
  const {
    label,
    placeholderFontSize,
    variant = 'form-input',
    showEmoji = true,
    className,
    value = '',
    defaultValue,
    onChange,
    onEnterKeyDown,
    error,
    isFocus,
    disabled,
    disabledReadOnly,
    onFocus,
    onBlur,
    helperText,
    isEnabledHashtags = true,
    isEnabledMention = true,
    changeWithDebounce = true,
    magicUrl = true,
    replyMention,
    fixMentionDropdown = true,
    mentionDropDownPosition,
    disableNewLineWithEnter = false,
    emojiClassName,
    defaultHashtag,
    spaceFromEmoji,
    maxLength,
    labelProps = {},
    noBorder,
    emojiProps,
    isModalView,
    isInFooter = false,
    noLeft,
    additionalComponent,
    emojiPickerClassName,
    isSidebar,
    templateTexts = [],
    topComponents,
    dynamicTemplates = [],
    singleLine,
    ref,
  } = props;

  const { isTabletAndLess } = useMedia();
  const [showModal, setShowModal] = useState(false);
  const { theme } = useTheme();
  const { t } = useTranslation();
  const LimitCharacterToChangeSize = 120;
  const mentionDenotationChars = useMemo(() => {
    const tempValue = [];
    if (isEnabledMention) tempValue.push('@');
    if (isEnabledHashtags) tempValue.push('#');
    if (dynamicTemplates?.length) tempValue.push('[');
    return tempValue;
  }, [isEnabledMention, isEnabledHashtags]);

  const [focusClassToLabel, setFocusClassToLabel] = useState(false);
  const [isSmallContentFontSize, setIsSmallFontSize] = useState(
    value?.length >= LimitCharacterToChangeSize
  );
  const [inFooterSpecs, setInFooterSpecs] = useState({
    height: 0,
    width: 0,
    left: 0,
  });

  const [isFocusedInContainer, setIsFocusedInContainer] = useState(false);
  const onResize = useCallback(() => {
    const {
      width = 0,
      height = 0,
      left = 0,
    } = document
      .querySelector(`.${MODAL_FOOTER_CLASSNAME}`)
      ?.getBoundingClientRect?.() || {};
    setInFooterSpecs({ width, height, left });
  }, []);

  useResizeObserver(
    onResize,
    (document.querySelector(`.${MODAL_FOOTER_CLASSNAME}`) as HTMLElement) ||
      null
  );

  const closeMentionOnScroll = (e: any) => {
    const el = document.querySelector(`.${classes.mentionContainerDesktop}`);
    if (!el) return;
    if (el?.contains?.(e?.target)) return;
    el?.parentNode?.removeChild(el);
  };
  const modules = {
    toolbar: ['link'],
    keyboard: singleLine
      ? {
          bindings: {
            enter: {
              key: 13,
              handler: function () {
                return false;
              },
            },
          },
        }
      : undefined,
    magicUrl:
      magicUrl === true
        ? {
            urlRegularExpression:
              /((https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s<]{2,}|www\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s<]{2,}|https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9]+\.[^\s<]{2,}|www\.[a-zA-Z0-9]+\.[^\s<]{2,}))/gi,
            globalRegularExpression:
              /((https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s<]{2,}|www\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s<]{2,}|https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9]+\.[^\s<]{2,}|www\.[a-zA-Z0-9]+\.[^\s<]{2,}))/gi,
          }
        : magicUrl || false,
    mention: {
      onClose: () => {
        // removeArrow();
        setInFooterSpecs({ width: 0, height: 0, left: 0 });
        window.removeEventListener('wheel', closeMentionOnScroll);
        window.removeEventListener('click', closeMentionOnScroll);
        window.removeEventListener('scroll', closeMentionOnScroll);
      },
      onOpen: () => {
        if (!isInFooter) {
          window.addEventListener('wheel', closeMentionOnScroll);
          window.addEventListener('click', closeMentionOnScroll);
          window.addEventListener('scroll', closeMentionOnScroll);
          return;
        }
        const {
          width = 0,
          height = 0,
          left = 0,
        } = document
          .querySelector(`.${MODAL_FOOTER_CLASSNAME}`)
          ?.getBoundingClientRect?.() || {};
        setInFooterSpecs({ width, height, left });
      },
      allowedChars: /^[A-Za-z\sÅÄÖåäö_]*$/,
      mentionDenotationChars,
      positioningStrategy: fixMentionDropdown ? 'fixed' : 'relative',
      defaultMenuOrientation: mentionDropDownPosition,
      mentionContainerClass: cnj(
        classes.mentionContainer,
        isInFooter &&
          cnj(
            classes.mentionContainerInFooter,
            classes.mentionContainerLeftZero
          ),
        isTabletAndLess && classes.mentionContainerLeftZero,
        !isInFooter && classes.mentionContainerDesktop,
        variant === 'comment-input' && classes.commentMention
      ),
      renderItem: (item: any, searchTerm: string) => {
        if (searchTerm === '@') {
          if (templateTexts?.length) {
            return renderToString(<Typography>{item.label}</Typography>);
          }
          return renderToString(
            <UserItem fillColor={theme.colors.colorIconEighth} item={item} />
          );
        }
        if (searchTerm === '#')
          return renderToString(
            <HashtagItem {...item} postsText={t('posts_lower')} />
          );
        return renderToString(<DynamicTemplateItem {...item} />);
      },
      // @ts-ignore
      source: debounceAsync(
        'richtext-mention-hashtag',
        async (
          searchTerm: string,
          renderItem: (item: any, searchTerm: string) => string,
          mentionChar: string
        ) => {
          let values;
          if (mentionChar === '@') {
            try {
              if (templateTexts?.length) {
                values = templateTexts.map((item) => ({
                  label: `[${t(item)}]`,
                  value: JSON.stringify({
                    value: `[${t(item)}]`,
                    userType: item,
                  }),
                }));
              } else {
                const d = await getUsers({
                  text: searchTerm,
                });
                values = d?.map((x) => {
                  const { username } = x;
                  const value =
                    x.userType === 'PERSON'
                      ? `${x.name} ${x.surname}`
                      : x.title || username;

                  return {
                    ...x,
                    value: JSON.stringify({
                      value,
                      username,
                      userType: x?.userType,
                    }),
                  };
                });
              }
            } catch (e) {
              console.warn(e);
              values = [];
            }
          }
          if (mentionChar === '#') {
            try {
              if (searchTerm?.length)
                values = await getHashtags({
                  text: searchTerm,
                });
              else {
                values = await getPopularHashtags();
              }
            } catch (e) {
              console.warn(e);
              values = [];
            }
          }
          if (mentionChar === '[') {
            if (searchTerm)
              values = dynamicTemplates.filter((variable) =>
                variable.value.toLowerCase().includes(searchTerm.toLowerCase())
              );
            else values = dynamicTemplates;
          }
          renderItem(values, mentionChar);
        },
        100
      ),
      // @ts-ignore
      onSelect(item, insertItem) {
        if (item.denotationChar === '[') {
          insertItem(item, false, {
            blotName: 'styled-mention',
          });
          return;
        }
        let { value } = item;
        try {
          value = JSON.parse(value);
        } catch (err) {}
        insertItem({
          ...item,
          userid: item.id,
          value: value.value || value,
          username: value.username || '',
          denotationChar:
            item.denotationChar === '@' ? '' : item.denotationChar,
          userType: value?.userType,
        });
      },
    },
    // hashtagFinder: true,
  };

  const { quill, quillRef, Quill } = useQuill({
    theme: 'bubble',
    modules,
    formats: ['mention', 'link', 'styled-mention'],
    placeholder: variant === 'form-input' ? '' : label,
    readOnly: disabledReadOnly || disabled,
  });

  if (Quill && !quill) {
    const Clipboard = Quill.import('modules/clipboard');
    Quill.register('modules/clipboard', Clipboard);
  }

  if (!isModalView)
    useImperativeHandle(ref, () => ({
      // This seems conditional... but in fact it is not breaking the rules
      focus: () => {
        quill?.focus();
      },
      reset: () => {
        quill?.setContents('');
      },
      quill,
      onChooseEmoji: chooseEmoji,
    }));

  useEffect(() => {
    if (quill) {
      if (defaultValue?.length) {
        if (variant === 'form-input') {
          setFocusClassToLabel(true);
        }
        const delta = quill.clipboard.convert(defaultValue);
        quill.setContents(delta, 'api');

        handleTextFontSize();
      }
    }
  }, [quill, defaultValue]);

  useEffect(() => {
    if (!quill) return;

    // @ts-ignore
    if (value) {
      if (variant === 'form-input') {
        setFocusClassToLabel(true);
      }

      const delta = quill.clipboard.convert(value);
      quill.setContents(delta, 'api');
    }
    quillRef.current.firstChild.onfocus = () => {
      setFocusClassToLabel(true);
      onFocus?.(true);
    };

    const focusOut = (e) => {
      onFocus?.(false);
      onBlur?.(e);
      const value = quillRef.current?.innerText;
      if (!value?.length) setFocusClassToLabel(false);
    };

    quillRef.current?.addEventListener('focusout', focusOut);

    quill.on('text-change', handleValueChange);

    if (isFocus) {
      quill?.focus();
      const length = quill.getLength();
      if (length) quill.setSelection(length + 2, Quill.sources.API);
    }
    if (disableNewLineWithEnter) disableEnterKey();
    quill.root.setAttribute('spellcheck', 'false');
    quill.clipboard.addMatcher(Node.ELEMENT_NODE, (node, delta) => {
      if (singleLine) {
        const newText = node.textContent?.replace(/\n/g, ' ') ?? '';
        const newDelta = new Delta();
        return newDelta.insert(newText);
      }
      return delta;
    });

    return () => {
      quill && quill.off('text-change');
      quillRef.current?.removeEventListener('focusout', focusOut);
    };
  }, [quill]);

  const disableEnterKey = () => {
    quill.keyboard.bindings[13].unshift({
      key: 13,
      handler: (range, context) => {
        onEnterKeyDown?.(getEditorText());
        return false;
      },
    });
  };

  const richTextEndAnchorRef = useRef<HTMLDivElement>();

  const insertMention = useCallback(
    (replyUser) => {
      if (quill?.selection) {
        const range = quill.selection.savedRange; // cursor position

        if (!range || range.length !== 0) return;
        const position = range.index;

        quill.insertEmbed(
          position,
          'mention',
          {
            denotationChar: replyUser.fullName ? '' : '@',
            index: 0,
            id: `mentionedSpan${replyUser.id}`,
            userid: replyUser.id,
            value: replyUser.fullName || replyUser.username,
            username: replyUser.username,
            isReplyToMention: true,
          },
          Quill?.sources.API
        );
        quill.insertText(position + 1, ' ', Quill.sources.API);

        focusAndScroll(richTextEndAnchorRef.current);

        setTimeout(() => {
          quill.setSelection(position + 2, Quill.sources.API);
        }, 500);
      }
    },
    [Quill?.sources.API, quill]
  );

  const refMention = useRef('');
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (replyMention && quill) {
      const mentionSpan = containerRef.current?.querySelectorAll(
        `span[data-id="mentionedSpan${refMention.current.id}"]`
      );
      if (mentionSpan?.length) {
        const ele = mentionSpan[0];
        ele.dataset.id = `mentionedSpan${replyMention.id}`;
        ele.dataset.userid = replyMention.id;
        ele.dataset.value = mentionSpan;
        ele.innerHTML = `
          <span contenteditable="false">
            ${
              replyMention.fullName
                ? ''
                : '<span class="ql-mention-denotation-char">@</span>'
            }
            ${replyMention.fullName || replyMention.username}
          </span>
        `;
      } else {
        insertMention(replyMention);
      }
      refMention.current = replyMention;
    }
  }, [replyMention, quill, insertMention]);

  useEffect(
    () => () => {
      if (isModalView && isTabletAndLess) {
        document.body.focus();
        document.activeElement?.blur?.();
      }
    },
    []
  );

  useEffect(() => {
    if (defaultHashtag && quill && !replyMention && !defaultValue) {
      const value = `<p><br/></p>`;
      const delta = quill.clipboard.convert(value);

      quill.setContents(delta, 'silent');
      if (quill?.selection) {
        const range = quill.selection.savedRange; // cursor position

        if (!range || range.length != 0) return;
        const position = range.index;
        quill.insertEmbed(
          position + 1,
          'mention',
          {
            denotationChar: '#',
            index: 0,
            id: defaultHashtag,
            value: defaultHashtag,
          },
          Quill.sources.API
        );
        quill.setSelection(0, Quill.sources.API);
      }
    }
  }, [defaultHashtag, quill]);

  const onChangeWithDebounce = debounceAsync(
    'richtext-input',
    (inputValue) => onChange?.(inputValue),
    250
  );

  const getEditorText = () =>
    !quill.getText().split('\n').join('').length ? '' : quill.root.innerHTML;

  const handleTextFontSize = () => {
    const text = quillRef.current?.innerText;
    if (text.length >= LimitCharacterToChangeSize) {
      setIsSmallFontSize(true);
    } else {
      setIsSmallFontSize(false);
    }
  };

  function handleValueChange({
    innerText: _innerText,
  }: {
    innerText?: string;
  }) {
    if (showModal && !!quill) {
      quill.clipboard.dangerouslyPasteHTML(_innerText || '');
      const length = quill.getLength();
      if (length) quill.setSelection(length + 2, Quill.sources.API);
    }
    const innerText = _innerText || quillRef.current?.innerText;
    // const { ops } = quill.getContents();
    // const value = ops[0].insert;
    // @ts-ignore
    const contentPureLength = removeHtmlTagsInstring(innerText)?.length;
    const isExceeding = contentPureLength > maxLength;
    if (isExceeding && !!quill) {
      const diff = contentPureLength - maxLength;
      quill.deleteText(contentPureLength - diff, diff);
    }

    const countChars = innerText?.length;
    if (variant === 'form-input' && countChars > 1) {
      setFocusClassToLabel(true);
    }
    handleTextFontSize();
    // @ts-ignore
    const returnValue = getEditorText();
    const v = returnValue === initialValue ? '' : returnValue;
    if (changeWithDebounce) onChangeWithDebounce(v);
    else onChange(v);
  }

  function handleClick() {
    quill?.focus();
    setFocusClassToLabel(true);
    onFocus?.(true);
    if (variant === 'form-input' && isTabletAndLess && !isModalView) {
      setShowModal(true);
    }
  }

  function handleFocus() {
    setIsFocusedInContainer(() => true);
  }

  function handleBlur() {
    setIsFocusedInContainer(() => false);
  }

  function chooseEmoji(value: any) {
    const range = quill.getSelection(true);
    if (range) {
      quill.deleteText(range.index, range.length);
    }
    if (!value?.native) alert('No emoji value');
    const rangIndex = range ? range.index : 0;
    quill.insertText(rangIndex, `${value.native} `);
  }

  const labelColor = error ? 'error' : isFocus ? 'brand' : 'border';

  const styles = useCssVariables({
    scope: classes.richTextEditor,
    variables: {
      placeholderFontSize: `${placeholderFontSize}px`,
      inFooterHeight: `${inFooterSpecs?.height}px`,
      inFooterWidth: `${inFooterSpecs?.width}px`,
      inFooterleft: `${noLeft ? 0 : inFooterSpecs?.left}px`,
    },
  });

  const tempValue = useRef('');
  const handleTempChange = (val: string) => {
    tempValue.current = val;
  };
  const hasValue =
    !isEmpty(quillRef.current?.innerText) &&
    quillRef.current?.innerText !== '\n';

  return (
    <>
      {styles}
      <div
        aria-hidden="true"
        ref={containerRef}
        className={cnj(
          classes.richTextEditor,
          (disabled || disabledReadOnly) && classes.richTextEditorDisabled,
          disabledReadOnly && classes.richTextEditorDisabledReadOnly,
          isFocusedInContainer && !disabled && !disabledReadOnly && 'focused',
          variant === 'form-input' ||
            variant === 'comment-input' ||
            disabledReadOnly
            ? classes.longText
            : isSmallContentFontSize
              ? classes.longText
              : classes.shortText,
          !noBorder && variant === 'form-input' && classes.borderStyle,
          !noBorder &&
            variant === 'form-input' &&
            !disabledReadOnly &&
            isFocus &&
            classes.focusedBorder,
          !noBorder &&
            variant === 'form-input' &&
            !disabledReadOnly &&
            error &&
            classes.errorBorder,
          !noBorder &&
            variant === 'comment-input' &&
            !disabledReadOnly &&
            classes.commentInputStyle,
          isModalView && classes.richTextEditor_isModalView,
          className
        )}
        onClick={handleClick}
        onFocus={handleFocus}
        onBlur={handleBlur}
      >
        {topComponents}
        {variant === 'form-input' && (
          <label
            htmlFor="quill-input-ref"
            className={cnj(
              classes.floatingLabel,
              focusClassToLabel ? classes.floatingLabelFocus : null
            )}
          >
            <Typography
              size={15}
              font="400"
              color={labelColor}
              isTruncated={focusClassToLabel}
              {...labelProps}
            >
              {label}
            </Typography>
          </label>
        )}
        {error && variant === 'form-input' && (
          <Icon
            type="fas"
            name="exclamation-circle"
            className={cnj(classes.bell, classes.toggle, classes.cursorDefault)}
          />
        )}
        <div
          ref={quillRef}
          // id="quill-input-ref"
          className={cnj(
            'richtext_editor',
            spaceFromEmoji && classes.emojiPadding
          )}
        />
        <Flex
          ref={richTextEndAnchorRef}
          className={classes.richTextEndAnchor}
        />

        {(showEmoji || additionalComponent) && (
          <Flex
            className={cnj(
              classes.fontContainer,
              hasValue ? classes.showOnBottom : classes.showOnRight,
              emojiClassName
            )}
          >
            {showEmoji && (
              <EmojiPickerButton
                onEmojiSelect={chooseEmoji}
                emojiClassName={cnj(
                  classes.emojiPickerClassName,
                  emojiPickerClassName
                )}
                placement="top-end"
                emojiProps={{ disabled, ...emojiProps }}
                isSidebar={isSidebar}
              />
            )}
            {additionalComponent}
          </Flex>
        )}
      </div>

      {(!!error || helperText) && (
        <Typography
          size={13}
          height={15}
          color="border"
          className={cnj(classes.helperText, error && classes.errorText)}
        >
          {error ? t(error) : helperText}
        </Typography>
      )}
      {maxLength && (
        <Typography
          size={13}
          height={15}
          color="border"
          textAlign="right"
          className={classes.maxLength}
        >{`${
          removeHtmlTagsInstring(value?.toString())?.length || 0
        }/${maxLength}`}</Typography>
      )}
      {showModal && (
        <FixedRightSideModal modalClassName={cnj(classes.modalRoot)}>
          <ModalHeaderSimple
            visibleHeaderDivider
            title={t(label || '')?.replace(t('(optional)'), '')}
            hideBack={false}
            backButtonProps={{
              onClick: (e: any) => {
                // window.focus();
                // inputRef?.current?.blur?.();
                handleValueChange?.({ innerText: quillRef.current?.innerText });
                setShowModal(false);
                setTimeout(() => {
                  document.activeElement?.blur?.();
                  onFocus?.(false);
                  onBlur?.(e);
                }, 200);
              },
            }}
            noCloseButton
          />
          <ModalBody>
            <RichText
              {...props}
              onChange={handleTempChange}
              ref={ref}
              isModalView
            />
          </ModalBody>
          <ModalFooter className={classes.row}>
            <Button
              fullWidth
              label={t('discard')}
              schema="gray"
              onClick={() => {
                handleValueChange?.({ innerText: quillRef.current?.innerText });
                setShowModal(false);
                document.activeElement?.blur?.();
              }}
            />
            <Button
              fullWidth
              label={t('done')}
              schema="primary-blue"
              onClick={() => {
                handleValueChange?.({ innerText: tempValue.current });
                setShowModal(false);
                document.activeElement?.blur?.();
              }}
            />
          </ModalFooter>
        </FixedRightSideModal>
      )}
    </>
  );
};

export default RichText;

function renderToString(element: ReactElement) {
  const container = document.createElement('div');
  const root = createRoot(container);
  flushSync(() => {
    root.render(element);
  });
  return container.innerHTML;
}
