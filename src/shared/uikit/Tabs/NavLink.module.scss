@use 'sass:list';

@import '/src/shared/theme/theme.scss';

@layer uikit {
  .linkRoot {
    display: inline-block;
    text-align: center;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    border-image-width: 0 !important;
    border-image: unset !important;

    &:focus,
    &:hover,
    &:visited,
    &:link,
    &:active {
      text-decoration: none;
      outline: none;
    }
  }

  .isFirstLink {
    margin-left: 0 !important;
  }

  .title {
    padding: 0 variables(gutter);
  }
  .title {
    padding: 0 variables(xLargeGutter) * 0.5 0 variables(gutter) * 0.5;
  }

  .underLinLink {
    padding: variables(xLargeGutter) * 0.5 0 0 0;
    font-style: normal;
    font-size: 15px;
    line-height: 20px;
    color: colors(thirdText) !important;
    font-weight: normal;
    font-family: fonts(regular);
    transition: transitions(smooth);
    height: 48px;
  }

  .badgeLink {
    display: flex;
    padding: 7px variables(gutter);
    margin-left: variables(gutter) * 0.5;
    font-style: normal;
    font-size: 15px;
    line-height: 18px;
    color: colors(primaryText);
    font-weight: 400;
    font-family: fonts(bold), serif;
    background-color: colors(backgroundIconSecondary);
    border-radius: 100px;
    transition: transitions(smooth);
  }

  .fullWidthLink {
    min-width: 40px;
    flex: 1;

    &:after {
      width: 100%;
    }
  }

  .disabledLink {
    opacity: 0.3;
    pointer-events: none;
  }

  .activeLink {
    cursor: default;
    font-family: fonts(bold), serif;
    color: colors(brand) !important;
    transition: transitions(smooth);
  }

  .activeBadgeLink {
    cursor: default;
    color: colors(backgroundIconSecondary);
    background-color: colors(primaryText);
    transition: transitions(smooth);
  }

  .badgeWrapper {
    padding: 4px 12px 4px 4px;
    display: flex;
    flex-direction: row !important;
    align-items: center !important;
  }

  .badge {
    width: 24px;
    height: 24px;
    min-width: 24px;
    min-height: 24px;
    max-height: 24px;
    max-width: 24px;
    margin-right: variables(list.slash(gutter, 2));
  }
  .indicator {
    margin-top: 12px;
    height: 4px;
    max-height: 4px;
    border-top-right-radius: 4px;
    border-top-left-radius: 4px;
    background: colors(transparent);
  }
  .activeBackground {
    background: colors(brand);
  }

  @media (min-width: breakpoints(tablet)) {
    .fullWidthLink {
      min-width: unset;
      flex: unset;

      &:after {
        width: var(--width);
      }
    }

    .underLinLink {
      &:hover {
        background-color: colors(hoverPrimary);
        color: colors(thirdText) !important;
      }
    }

    .badgeLink {
      &:hover {
        background-color: colors(hoverThird);
        color: colors(primaryText);
      }
    }
    .activeLink {
      &:hover {
        background: unset;
        color: colors(brand) !important;
      }
    }

    .activeBadgeLink {
      &:hover {
        color: colors(backgroundIconSecondary);
        background-color: colors(primaryText);
      }
    }
  }
}
