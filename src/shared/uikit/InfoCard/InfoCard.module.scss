@import '/src/shared/theme/theme.scss';
@layer uikit2 {
  .wrapper {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: variables(gutter) * 0.5;
    border-radius: 8px;
    background-color: colors(warning_10);

    &.color-warning {
      background-color: colors(warning_10);
    }
    &.color-gray {
      background-color: colors(gray_10);
    }
  }
  .leftWrapper {
    flex-direction: row;
    align-items: center;
  }
  .leftIcon {
    margin-right: variables(gutter) * 0.5;
  }
  .label {
    font-size: 14px;
    line-height: 16px;
    font-weight: 500;
    color: colors(smoke_coal);
    &.color-warning {
      color: colors(pendingOrange);
    }
    &.color-gray {
      color: colors(disabledGrayDark);
    }
  }
  .rightIcon {
    cursor: pointer;
    height: 18px;
  }

  @media (min-width: breakpoints(tablet)) {
    .wrapper {
    }
  }
}
