import React from 'react';
import type { ComponentProps, ReactNode } from 'react';
import classes from './InfoCard.module.scss';
import Icon from '../Icon';
import Flex from '../Flex';
import cnj from '../utils/cnj';
import Typography, { type TypographyProps } from '../Typography';

export type InfoCardProps = {
  label?: string;
  leftIconProps?: ComponentProps<typeof Icon>;
  rightIconProps?: ComponentProps<typeof Icon>;
  labelProps?: Omit<TypographyProps, 'children'>;
  classNames?: {
    wrapper?: string;
    leftWrapper?: string;
    leftIcon?: string;
    label?: string;
    rightIcon?: string;
  };
  children?: ReactNode;
  renderRight?: () => ReactNode;
  hasLeftIcon?: boolean;
  color?: 'warning' | 'gray';
};

export function InfoCard({
  label = '',
  classNames = {},
  leftIconProps = {},
  rightIconProps = {},
  labelProps = {},
  children,
  renderRight,
  hasLeftIcon = true,
  color = 'warning',
}: InfoCardProps): JSX.Element {
  const { name: leftName = 'exclamation-triangle', type: leftType = 'far' } =
    leftIconProps;
  const { name: rightName = 'times', type: rightType = 'far' } = rightIconProps;
  const hasRightIcon = !!Object.keys(rightIconProps)?.length;

  return (
    <Flex
      className={cnj(
        classes.wrapper,
        classNames.wrapper,
        classes[`color-${color}`]
      )}
    >
      <Flex className={cnj(classes.leftWrapper, classNames.leftWrapper)}>
        {hasLeftIcon && leftIconProps && (
          <Icon
            color={color === 'warning' ? 'pendingOrange' : 'disabledGrayDark'}
            type={leftType}
            name={leftName}
            size={24}
            {...leftIconProps}
            className={cnj(classes.leftIcon, classNames.leftIcon)}
          />
        )}

        <Typography
          className={cnj(
            classes.label,
            classNames.label,
            classes[`color-${color}`]
          )}
          {...labelProps}
        >
          {label}
          {children}
        </Typography>
      </Flex>
      {!!renderRight && renderRight()}
      {hasRightIcon && (
        <Icon
          {...rightIconProps}
          type={rightType}
          name={rightName}
          className={cnj(classes.rightIcon, classNames.rightIcon)}
        />
      )}
    </Flex>
  );
}
