import { useFormikContext } from 'formik';
import Button from '../Button';
import type { ButtonProps } from '../Button';
import type { IconName } from '../Icon';
import type {
  IconButtonColorSchema,
  IconButtonSize,
} from '../Button/IconButton';
import IconButton from '../Button/IconButton';
import { scrollToFirstError } from '@shared/utils/form/formValidator/scrollToFirstError';

export interface SubmitButtonProps extends ButtonProps {
  active?: boolean;
  disabled?: boolean;
  type?: string;
  style?: string;
  iconName?: IconName;
  iconButtonSize?: IconButtonSize;
  iconButtonColorSchema?: IconButtonColorSchema;
}

const SubmitButton: React.FC<SubmitButtonProps> = ({
  active,
  iconName,
  iconButtonSize,
  iconButtonColorSchema,
  ...rest
}) => {
  const { isSubmitting, handleSubmit, errors } = useFormikContext();
  const disabled = active ? isSubmitting : isSubmitting || rest.disabled;

  const onClick = (e: React.FormEvent<HTMLFormElement>) => {
    handleSubmit(e);
    scrollToFirstError(errors);
  };

  return iconName ? (
    <IconButton
      {...rest}
      onClick={onClick}
      disabled={disabled}
      isLoading={isSubmitting}
      name={iconName}
      size={iconButtonSize}
      colorSchema={iconButtonColorSchema}
    />
  ) : (
    <Button
      {...rest}
      onClick={onClick}
      disabled={disabled}
      isLoading={isSubmitting}
    />
  );
};

export default SubmitButton;
