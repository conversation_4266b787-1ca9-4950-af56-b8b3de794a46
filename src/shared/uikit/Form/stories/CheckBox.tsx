import type { StoryObj } from '@storybook/react';
import { useScheduleFormFieldOptions } from '@shared/hooks/schedules/useScheduleFormFieldOptions';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { type CheckBoxGroupProps } from '@shared/uikit/CheckBoxGroup';
import DynamicFormBuilder from '../DynamicFormBuilder';
import Form from '..';
import SubmitButton from '../SubmitButton';
import type meta from './meta';

type Story = StoryObj<typeof meta>;

const checkboxProps: Partial<CheckBoxGroupProps> = { disabledReadOnly: false };

export const Checkbox: Story = {
  args: checkboxProps,
  argTypes: {},
  parameters: {
    controls: {
      expanded: true,
    },
  },
  render: ({ disabledReadOnly }) => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const { attendeePermissionsOptions } = useScheduleFormFieldOptions();
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const { t } = useTranslation();

    const fields = [
      {
        name: 'attendeePermissions',
        formGroup: {
          color: 'smoke_coal',
          title: t('attendee_permissions'),
        },
        cp: 'checkBoxGroup',
        options: attendeePermissionsOptions,
        label: t('attendee_permissions'),
        disabledReadOnly,
        onChange(newValue: typeof attendeePermissionsOptions, { meta }: any) {
          const seeGuestsListOption = attendeePermissionsOptions[2];
          const hasGuestListOption = newValue.find(
            (item) => item.value === seeGuestsListOption.value
          );

          if (newValue.length > 0 && !hasGuestListOption) {
            if (meta.value?.includes(seeGuestsListOption)) {
              newValue.length = 0;
            } else {
              newValue.push(seeGuestsListOption);
            }
          }
        },
      },
    ];
    const initialValues = {
      attendeePermissions: [],
    };
    return (
      <Form
        key="description"
        initialValues={initialValues}
        apiFunc={async (data) => {
          // eslint-disable-next-line no-alert
          window.alert(`form submitted\n${JSON.stringify(data)}`);
          return {};
        }}
        onSuccess={() => console.log('success')}
        enableReinitialize
        className="w-[400px] gap-12"
      >
        <DynamicFormBuilder groups={fields} />
        <SubmitButton label="Submit" />
      </Form>
    );
  },
};
