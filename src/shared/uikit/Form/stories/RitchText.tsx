import type { StoryObj } from '@storybook/react';
import { useFormikContext } from 'formik';
import Text from '@shared/components/molecules/Text/Text';
import DynamicFormBuilder from '../DynamicFormBuilder';
import Form from '..';
import { type RichTextProps } from '../../RichText/RichText.component';
import SubmitButton from '../SubmitButton';
import type meta from './meta';

type Story = StoryObj<typeof meta>;

const ritchTextProps: Partial<RichTextProps> = {
  label: 'description',
  maxLength: 1000,
  visibleCharCounter: true,
  showEmoji: false,
  disabledReadOnly: false,
  isEnabledHashtags: true,
  isEnabledMention: true,
  magicUrl: true,
  variant: 'form-input',
};

const DescriptionDisplay = () => {
  const { values } = useFormikContext();
  const descriptionValue = values ? (values as any).description : undefined;
  return (
    <Text
      label="description (display mode)"
      value={descriptionValue}
      isRichText
    />
  );
};

export const Description: Story = {
  args: ritchTextProps,
  argTypes: {
    variant: {
      control: 'select',
      options: ['form-input', 'comment-input', 'simple'],
    },
    magicUrl: {
      description: `boolean or MagicUrlOptions, will not affect the component after initialization.`,
    },
  },
  parameters: {
    controls: {
      expanded: true,
    },
  },
  render: (args) => {
    const fields = [
      {
        cp: 'richtext',
        name: 'description',
        label: 'description',
        maxLength: 1000,
        visibleCharCounter: true,
        showEmoji: false,
        disabledReadOnly: false,
        isEnabledHashtags: true,
        isEnabledMention: true,
        magicUrl: true,
        ...args,
        className: 'min-h-[300px] max-h-[400px]',
      },
    ];
    const initialValues = {
      description: `<p><a href="http://www.link.com" rel="noopener noreferrer" target="_blank">www.link.com</a></p><p><span class="mention" data-index="0" data-denotation-char="" data-id="50000023749" data-value="User Last Name" data-userid="50000023749" data-username="user_last_name_r14697gd4" data-user-type="PERSON">&#xFEFF;<span contenteditable="false"><span class="ql-mention-denotation-char"></span>User Last Name</span>&#xFEFF;</span> </p>`,
    };
    return (
      <Form
        key="description"
        initialValues={initialValues}
        apiFunc={async (data) => {
          window.alert(`form submitted\n${JSON.stringify(data)}`);
          return {};
        }}
        onSuccess={() => console.log('success')}
        enableReinitialize
        className="w-[400px] gap-12"
      >
        <DynamicFormBuilder groups={fields} />
        <DescriptionDisplay />
        <SubmitButton label="Submit" />
      </Form>
    );
  },
};
