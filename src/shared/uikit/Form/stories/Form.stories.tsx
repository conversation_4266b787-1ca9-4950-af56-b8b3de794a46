import type { ArgTypes, Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import { type ComponentProps } from 'react';
import { GENDER_VALUES } from '@shared/utils/constants/enums';
import Flex from '@shared/uikit/Flex';
import Form from '..';
import DynamicFormBuilder from '../DynamicFormBuilder';
import SubmitButton from '../SubmitButton';

type FormProps = ComponentProps<typeof Form>;
type DynamicFormBuilderProps = ComponentProps<typeof DynamicFormBuilder>;
type Props = FormProps & DynamicFormBuilderProps;

const meta: Meta<Props> = {
  title: 'Atoms/Form/DropdownSelect',
  component: Form,
} satisfies Meta<Props>;

export default meta;
type Story = StoryObj<typeof meta>;

const defaultProps: Partial<Props> = {
  onSuccess: fn(),
  initialValues: { name: GENDER_VALUES.FEMALE },
  groups: [
    {
      name: 'name',
      cp: 'dropdownSelect',
      options: Object.values(GENDER_VALUES),
    },
  ],
};

const defaultArgTypes: Partial<ArgTypes<Props>> = {
  local: {
    control: false,
  },
  groups: {
    control: false,
  },
};

export const Playground: Story = {
  render: ({ groups, ...props }) => (
    <Form {...props} local>
      <Flex className="gap-20">
        <DynamicFormBuilder groups={groups} />
        <SubmitButton label="submit" />
      </Flex>
    </Form>
  ),
  args: defaultProps,
  argTypes: defaultArgTypes,
};
