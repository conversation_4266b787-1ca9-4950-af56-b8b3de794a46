import type { colorsKeys } from 'shared/uikit/helpers/theme';
import type {
  ApplicantBaseType,
  ApplicantType,
  CandidateType,
  JobAPIProps,
  SingleJobAPIProps,
} from './jobsProps';

export type StageType =
  | 'REVIEW'
  | 'INTERVIEW'
  | 'OFFERED'
  | 'HIRED'
  | 'ON_BOARDED'
  | 'CUSTOMIZE';

export type StageContextType = 'HIRING' | 'ONBOARDING';

export type StageColor = Extract<
  colorsKeys,
  'error' | 'brand' | 'heliotrope' | 'success' | 'pendingOrange'
>;

export interface PipelineInfo {
  id: string;
  title: string;
  type: StageType;
  stageType: StageContextType;
  applicantTrack: boolean;
  order: number;
  color: StageColor;
  count: number;
}

export interface JobPipelinesProps {
  id: string;
  job: SingleJobAPIProps;
  stages: PipelineInfo[];
}

export type BulkActionType =
  | 'move_to'
  | 'reject'
  | 'interview'
  | 'email'
  | 'message'
  | 'note'
  | 'todo_1'
  | 'todo_2';

export interface BulkItemProps<T> {
  value: T;
  label: string;
}

export interface BulkPipelineFormProps {
  action?: BulkItemProps<BulkActionType>;
  move_to?: BulkItemProps<string>;
  reject_template?: BulkItemProps<string>;
  interview_availability?: BulkItemProps<string>;
  interview_template?: BulkItemProps<string>;
  email_template?: BulkItemProps<string>;
  message_template?: BulkItemProps<string>;
  note_visibility?: 'TEAM' | 'ONLY_ME';
  note_text?: string;
  note_attachment?: { id: string }[];
  todo_title?: string;
  todo_text?: string;
  todo_start_date?: string;
  todo_start_time?: { label: string; value: string };
  todo_end_date?: string;
  todo_end_time?: { label: string; value: string };
  todo_status?: BulkItemProps<'OPEN' | 'ON_HOLD' | 'DONE'>;
  todo_assignee?: BulkItemProps<number>;
  todo_attachment?: { id: string }[];
}

export interface BulkPipelineFormErrorProps {
  action?: string;
  move_to?: string;
  reject_template?: string;
  interview_availability?: string;
  interview_template?: string;
  email_template?: string;
  message_template?: string;
  note_visibility?: string;
  note_text?: string;
  todo_title?: string;
  todo_text?: string;
}

export interface PipelineParticipantProps extends ApplicantBaseType {
  applicant?: ApplicantType;
  candidate?: CandidateType;
}

export interface JobPiplineData {
  job: JobAPIProps;
  pipelines: PipelineInfo[];
}
