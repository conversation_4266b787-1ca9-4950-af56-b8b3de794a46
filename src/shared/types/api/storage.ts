export interface UploadFileResponse {
  id: string;
  createdDate: string;
  lastModifiedDate: string;
  version: number;
  originalFileName: string;
  fileSize: number;
  contentType: string;
  ownerUserId: number;
  sharedWithUserIds: number[];
  link: string;
  secondaryLink: string;
  bytes: string[];
}

export interface CandidateUploadFileResponse {
  value: string;
  default: boolean;
  originalFile: {
    handle: any;
    path: string;
    relativePath: string;
  };
}

export interface PostPrivateFileResponse extends UploadFileResponse {
  private: true;
}
