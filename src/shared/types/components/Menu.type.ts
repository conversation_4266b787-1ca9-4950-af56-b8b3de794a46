import type { PopperPlacementType } from '@mui/material';
import type { MouseEvent, Ref } from 'react';
import type { IconName } from 'shared/uikit/Icon';
import type { IconButtonColorSchema } from 'shared/uikit/types';
import type { IconButtonSize } from 'shared/uikit/Button/IconButton';
import type { PopperMenuProps } from './PopperMenu.type';

export interface MenuItem {
  iconName: IconName;
  iconSize?: number;
  className?: string;
  onClick?: (e: MouseEvent) => void;
  label: string;
  tooltipTitle?: React.ReactNode | string;
  disabled?: boolean;
}
export interface MenuProps {
  menuItems: MenuItem[];
  menuItemSize?: number;
  menuIcon?: IconName;
  menuIconSize?: IconButtonSize;
  menuIconColorSchema?: IconButtonColorSchema;
  menuPlacement?: PopperPlacementType;
  className?: string;
  popperMenuProps?: Partial<PopperMenuProps>;
  classNames?: {
    itemIconWrapper?: string;
    itemIcon?: string;
    menu?: string;
  };
  ref?: Ref<any>;
}
