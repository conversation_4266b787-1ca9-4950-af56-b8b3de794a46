import type { LanguageNormalizerType } from '@shared/utils/normalizers/languageNormalizer';
import type { NormalizedExperience } from '@shared/utils/experience.utils';
import type { Education } from './education';
import type { Experience } from './experience';
import type {
  AgeRangeType,
  CandidateStatusType,
  CriminalRecordType,
  DisabilityStatusType,
  GenderType,
  IDDocTypes,
  NoticePeriodType,
  RaceType,
  RelocationStatusType,
  ValueLabelType,
  VeteranStatusType,
} from './general';
import type { Language } from './language';
import type { BELocation } from './lookup';
import type { BESkill } from './skill';

import type {
  EmploymentType,
  ExperienceLevelType,
  PeriodType,
  WillingToTravelType,
  WorkPlaceType,
} from './generalProps';
import type { AutoCompleteOption } from './components/Form.type';
import type { TodoProps, TodoStatusType } from './todo';
import type { UserTypeInList } from './user';
import type { BENote, INote, NoteRequest } from './note';
import type { ReviewRequest, BEReview, IReview } from './review';
import type { MeetingRequest, BEMeeting, IMeeting } from './meeting';

type FormDataLocation = BELocation & { value?: string | null };

export interface BECandidateSearchResult {
  id: string;
  createDateTime: string;
  pageId: string;
  profileId: string;
  name: string;
  surname: string;
  croppedImageUrl: null;
  occupation: string;
  location: BELocation;
  bio: string;
  skills: unknown[];
  languages: unknown[];
  experiences: unknown[];
  educations: unknown[];
  usagesCount: string;
  notesCount: string;
  todosCount: string;
  meetingsCount: string;
}

export interface ICandidateListItemProps {
  id: string;
  pageId: string;
  profileId: string;
  firstText: string;
  secondText: string;
  thirdText: string;
  fourthText?: string;
  avatar?: string;
  createDateTime: string;
  usagesCount: string;
  notesCount: string;
  todosCount: string;
  meetingsCount: string;
  isManual: boolean;
}

export interface CreateCandidateFormData {
  id: string;
  originalId: string;
  email: string;
  name: string;
  surname: string;
  username?: string;
  usernameAtSign?: string;
  fullName?: string;
  bio?: string;
  occupation?: { label: string; value?: string };
  location?: FormDataLocation;
  resumeUrl?: string;
  croppedImageUrl?: string;
  experiences?: Experience[];
}

export interface CandidateFormFlags {
  socialInformationEdited?: boolean;
  backgroundInformationEdited?: boolean;
  expectedInformationEdited?: boolean;
  legalInformationEdited?: boolean;
}

export interface CreateCandidateAPIRequestBody {
  email: string;
  name: string;
  surname: string;
  occupation?: string;
  occupationId?: string;
  location?: BELocation;
  resumeUrl?: string;
}

export interface CandidateSocialInfoAPIRequestBody {
  cellNumber?: string;
  workNumber?: string;
  homeNumber?: string;
  skypeId?: string;
  linkedinUrl?: string;
  facebookUrl?: string;
  twitterUrl?: string;
  otherUrls?: string[];
  fullAddress?: string;
}

export interface CandidateSocialInfoFormData {
  cellNumber?: string;
  workNumber?: string;
  homeNumber?: string;
  skypeId?: string;
  linkedinUrl?: string;
  facebookUrl?: string;
  twitterUrl?: string;
  linkedinUsername?: string;
  facebookUsername?: string;
  twitterUsername?: string;
  otherUrls?: string[];
  fullAddress?: string;
}

export interface CandidatePreferenceInfoAPIRequestBody {
  preferredJobTitle?: string;
  preferredEmploymentType?: EmploymentType;
  preferredWorkPlaceType?: WorkPlaceType;
  preferredExperienceLevel?: ExperienceLevelType;
  noticePeriod?: NoticePeriodType;
  preferredLocation?: BELocation;
  relocation?: RelocationStatusType;
  travelRequirement?: WillingToTravelType;
  expectedCurrencyId?: string;
  expectedCurrencyCode?: string;
  expectedCurrencyTitle?: string;
  expectedSalaryPeriod?: PeriodType;
  expectedMinimumSalary?: number;
  expectedMaximumSalary?: number;
}
export interface CandidatePreferenceInfoFormData {
  preferredJob?: ValueLabelType<string>;
  preferredEmploymentType?: ValueLabelType<EmploymentType>;
  preferredWorkPlaceType?: ValueLabelType<WorkPlaceType>;
  preferredExperienceLevel?: ValueLabelType<ExperienceLevelType>;
  noticePeriod?: ValueLabelType<NoticePeriodType>;
  preferredLocation?: FormDataLocation;
  relocation?: ValueLabelType<RelocationStatusType>;
  travelRequirement?: ValueLabelType<WillingToTravelType>;
  expectedCurrency?: ValueLabelType<string> & { code?: string };
  expectedSalaryPeriod?: ValueLabelType<PeriodType>;
  expectedMinimumSalary?: number;
  expectedMaximumSalary?: number;
}

export interface CandidateLegalInfoAPIRequestBody {
  country?: string;
  countryCode?: string;
  identificationDocumentType?: IDDocTypes;
  identificationDocumentTypeEnteredByUser?: boolean;
  ssn?: string;

  expectedTaxTermId?: number;
  expectedTaxTermTitle?: string;
  expectedMarkup?: number;
  criminalRecord?: CriminalRecordType;

  workAuthorizationId?: string;
  workAuthorizationTitle?: string;
  visaHeldByUserId?: string;
  visaHeldByUserName?: string;
  workAuthorizationExpiryDate?: Date | string;
  visaHeldByUserSurname?: string;
  visaHeldByUserCroppedImageUrl?: string;
  fileIds?: Array<number | string>;
}

interface UserAutoCompleteOptionExtra {
  name?: string;
  surname?: string;
  fullName?: string;
}

export interface CandidateLegalInfoFormData {
  identificationDocument?: ValueLabelType<IDDocTypes>;
  ssn?: string;
  workAuthorization?: ValueLabelType<string>;
  workAuthorizationExpiryDate?: Date;
  visaHeldByUser?: AutoCompleteOption & UserAutoCompleteOptionExtra;
  resumeUrl?: string;
  fileIds: string[];
  attachments?: number[];
  expectedTaxTerm?: ValueLabelType<number>;
  expectedMarkup?: number;
  criminalRecord?: ValueLabelType<CriminalRecordType>;
  country?: ValueLabelType<string>;
}

export interface CandidateResumeRequestBody {
  url: string;
}

export interface CandidateStatusInfoAPIRequestBody {
  resumeUrl?: string;
  tags?: string[];
  openToWork?: CandidateStatusType;
  note?: string;
  jobSite?: string;
}

export interface CandidateStatusInfoFormData {
  resumeUrl?: string;
  tags?: string[];
  openToWork?: ValueLabelType<CandidateStatusType>;
  note?: string;
  jobSite?: ValueLabelType<string>;
}

export interface CandidateDemographicInfoAPIRequestBody {
  gender?: GenderType;
  ageRange?: AgeRangeType;
  race?: RaceType;
  veteranStatus?: VeteranStatusType;
  disabilityStatus?: DisabilityStatusType;
  birthdate?: Date;
  referralUserId?: string;
  referralUserName?: string;
  referralUserSurname?: string;
  referralUserCroppedImageUrl?: string;
  referralCompanyId?: string;
  referralCompanyName?: string;
  referralCompanyCroppedImageUrl?: string;
  referralEmail?: string;
  referralPhone?: string;
  referralUrl?: string;
  ageRangeEnteredByUser?: boolean;
  raceEnteredByUser?: boolean;
  veteranStatusEnteredByUser?: boolean;
  disabilityStatusEnteredByUser?: boolean;
  genderEnteredByUser?: boolean;
  ssnEnteredByUser?: boolean;
}

export interface CandidateDemographicFormData {
  gender?: ValueLabelType<GenderType>;
  ageRange?: ValueLabelType<AgeRangeType>;
  race?: ValueLabelType<RaceType>;
  veteranStatus?: ValueLabelType<VeteranStatusType>;
  disabilityStatus?: ValueLabelType<DisabilityStatusType>;
  birthdate?: string | Date;
  referralUser?: AutoCompleteOption & UserAutoCompleteOptionExtra;
  referralCompany?: AutoCompleteOption;
  // Intentional (Emiil) typo to bypass form autofill
  ssn?: string;
  referralEmiil?: string;
  referralPhone?: string;
  referralUrl?: string;
  ageRangeEnteredByUser?: boolean;
  raceEnteredByUser?: boolean;
  veteranStatusEnteredByUser?: boolean;
  disabilityStatusEnteredByUser?: boolean;
  genderEnteredByUser?: boolean;
  ssnEnteredByUser?: boolean;
}

export interface CandidateAPIData
  extends CandidateDemographicInfoAPIRequestBody,
    CandidateLegalInfoAPIRequestBody,
    CandidateSocialInfoAPIRequestBody,
    CandidatePreferenceInfoAPIRequestBody,
    CandidateStatusInfoAPIRequestBody,
    CandidateFormFlags {
  id: string;
  createdDate: Date;
  lastModifiedDate: Date;
  version: string;
  creatorUserId: string;
  creatorPageId: string;
  isLoboxUser: boolean;
  isLoboxCandidate: boolean;
  profile: CreateCandidateAPIRequestBody & {
    id: string;
    createdDate: Date;
    lastModifiedDate: Date;
    version: string;
    originalId: string;
    username: string;
    croppedImageUrl: string;
    occupationName: string;
    birthDate: Date;
    experiences: Experience[];
    skills: BESkill[];
    languages: LanguageNormalizerType[];
    educations: Education[];
  };
  invited: boolean;
  countOfJobs: string;
  calendarEvents: BECandidateCalendarEvents[];
  notes: BECandidateNote[];
  reviews: BECandidateReview[];
  usagesCount: number;
  meetingsCount: string;
  todosCount: string;
  notesCount: string;
  isManuallyCreated: boolean;
  isSocialCandidate: boolean;
}

export interface CandidateFormData
  extends CandidateDemographicFormData,
    CandidateLegalInfoFormData,
    CandidateSocialInfoFormData,
    CandidateStatusInfoFormData,
    CandidatePreferenceInfoFormData,
    CandidateFormFlags {
  profile: CreateCandidateFormData;

  // from profile
  _experiences: NormalizedExperience[];
  _educations: Education[];
  _skills: Skill[];
  _languages: Language[];

  // direct pass (no change)
  id: string;
  isLoboxUser: boolean;
  isLoboxCandidate: boolean;
  isSocialCandidate: boolean;
  isManuallyCreated: boolean;
  createdDate?: Date;
  lastModifiedDate?: Date;
  creatorUserId: string;
  creatorPageId: string;
  calendarEvents: BECandidateCalendarEvents[];
  notes: BECandidateNote[];
  reviews: BECandidateReview[];
  meetingsCount: number;
  todosCount: number;
  notesCount: number;
  invited: boolean;
  countOfJobs: number;
  _isDuplicatdOnCreate?: true;
  coverLetter?: string;
  allowedToInviteAgain?: boolean;
}

export interface SimilarCandidate {
  category?: string;
  croppedImageUrl?: string;
  followersCounter?: string;
  followingsCounter?: string;
  fullName: string;
  hideIt: boolean;
  id: string;
  imageUrl?: string;
  name: string;
  occupationName?: string;
  surname: string;
  title?: string;
  userType: string;
  username: string;
}

interface BECandidateCalendarEvents {
  id: number;
  createdDate: Date;
  lastModifiedDate: Date;
  version: number;
  eventId: number;
  score: number;
  type?: 'MEETING';
}

export interface CandidateTodoRequest {
  title: string;
  description: string;
  assigneeUserId: number | string;
  start?: string;
  end?: string;
  status?: TodoStatusType;
  remind?: boolean;
}

export interface BECandidateTodo extends TodoProps {}
export interface ICandidateTodo {
  id: string;
  title: string;
  description: string;
  start?: string;
  startDate?: string;
  startTime?: ValueLabelType<string>;
  end?: string;
  endDate?: string;
  endTime?: ValueLabelType<string>;
  creator: UserTypeInList;
  creatorPageId: string;
  creatorType: string;
  assigneeUser: UserTypeInList;
  remind: boolean;
  status: ValueLabelType<TodoStatusType>;
}

export interface CandidateNoteRequest extends NoteRequest {}
export interface BECandidateNote extends BENote {}
export interface ICandidateNote extends INote {
  timeGroupUnix: number;
}

export interface CandidateReviewRequest extends ReviewRequest {}
export interface BECandidateReview extends BEReview {
  candidateId: string;
}
export interface ICandidateReview extends IReview {
  candidateId: string;
}

export interface CandidateMeetingRequest extends MeetingRequest {}
export interface BECandidateMeeting extends BEMeeting {}
export interface ICandidateMeeting extends IMeeting {}

export interface BEParticipanSummaries {
  id: number;
  userId: number;
  candidateId: number;
  type: 'APPLICANT' | 'CANDIDATE';
  jobTitle: string;
  pageId: number;
  pageTitle: string;
  pageCroppedImageUrl: string;
  dateTime: Date;
}
export interface BECandidateSummary {
  candidateId: number;
  candidateName: string;
  candidateSurname: string;
  candidateCroppedImageUrl: string;
  participationSummaries: BEParticipanSummaries[];
}

export interface ICandidateSummaryOption {
  candidateId?: number;
  jobTitle: string;
  pageCroppedImageUrl?: string;
  pageTitle: string;
  type: 'APPLICANT' | 'CANDIDATE' | 'ORIGINAL_CANDIDATE';
  dateTime?: Date;
  id?: string;
}

export type ICandidateSummaryOptions = ICandidateSummaryOption[];
export interface CandidateLastViewData {
  value: string;
}
