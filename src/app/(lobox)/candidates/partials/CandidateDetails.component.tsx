import SendMessageButton from '@shared/components/molecules/SendMessageButton';
import type { CandidateFormData } from '@shared/types/candidates';
import Button from '@shared/uikit/Button';
import {
  editCandidateAdditionalInfo,
  getCandidateById,
} from '@shared/utils/api/candidates';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import dynamic from 'next/dynamic';
import React, { useCallback, useEffect, useState, type FC } from 'react';
import Tabs from 'shared/components/Organism/Tabs';
import CandidateCard from 'shared/components/molecules/CandidateCard/CandidateCard';
import Flex from 'shared/uikit/Flex';
import { QueryKeys } from 'shared/utils/constants';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';
import type { CandidateManagerTabkeys } from '@shared/components/Organism/CandidateManager/tabs';
import HorizontalTagList from '@shared/components/molecules/HorizontalTagList';
import CardBadge from '@shared/components/molecules/CardBadge';
import Tooltip from '@shared/uikit/Tooltip';
import DateView from '@shared/uikit/DateView';
import Typography from '@shared/uikit/Typography';
import { useQueryClient } from '@tanstack/react-query';
import cleanRepeatedWords from '@shared/utils/toolkit/cleanRepeatedWords';
import { CandidateCardActions } from '@shared/components/molecules/CandidateCard';
import { IsManualWrapper } from '@shared/components/molecules/IsManualWrapper';
import classes from './CandidateDetails.module.scss';
import CandidateDetailsSkeleton from './CandidateDetails.skeleton';
import type { BaseCandidateSectionProp } from './types';
import CandidateAboutSkeleton from './tab1.CandidateAbout/CandidateAbout.skeleton';
import SimilarCandidatesSkeleton from './tab4.CandidateSimilar/CandidateSimilar.skeleton';
import CandidateActivitesSkeleton from './tab5.CandidateActivites/CandidateActivites.skeleton';
import CandidateJobsSkeleton from './tab2.CandidateJobs/CandidateJobs.skeleton';
import CandidateAvailabilityTab from './tab3.CandidateAvailability';

const CandidateAboutTab = dynamic(() => import('./tab1.CandidateAbout'), {
  loading: () => <CandidateAboutSkeleton />,
});

const CandidateJobsTab = dynamic(() => import('./tab2.CandidateJobs'), {
  ssr: false,
  loading: () => <CandidateJobsSkeleton />,
});

const CandidateSimilarTab = dynamic(() => import('./tab4.CandidateSimilar'), {
  loading: () => <SimilarCandidatesSkeleton />,
});
const CandidateActivitesTab = dynamic(
  () => import('./tab5.CandidateActivites'),
  {
    loading: () => <CandidateActivitesSkeleton />,
  }
);
const CandidateInsightsTab = dynamic(() => import('./tab6.CandidateInsights'));

type CandidateDetailsTabsNames =
  | 'about'
  | 'jobs'
  | 'availability'
  | 'similar'
  | 'activities'
  | 'insights';

interface CandidateDetailsProps {
  candidateId?: string | null;
  parentLoading?: boolean;
}

const tabs: Array<{ path: CandidateDetailsTabsNames; title: string }> = [
  {
    path: 'about',
    title: 'about',
  },
  {
    path: 'jobs',
    title: 'jobs',
  },
  {
    path: 'availability',
    title: 'availability',
  },
  {
    path: 'similar',
    title: 'similar',
  },
  {
    path: 'activities',
    title: 'activities',
  },
  {
    path: 'insights',
    title: 'insights',
  },
];

const CandidateDetails: FC<CandidateDetailsProps> = ({
  candidateId,
  parentLoading,
}) => {
  const { t } = useTranslation();

  const appDispatch = useGlobalDispatch();
  const [selectedTab, setSelectedTab] =
    useState<CandidateDetailsTabsNames>('about');

  useEffect(() => {
    setSelectedTab('about');
  }, [candidateId]);

  const { data: candidate, isLoading } = useReactQuery<CandidateFormData>({
    action: {
      apiFunc: () => getCandidateById(candidateId!),
      key: [QueryKeys.getCandidate, candidateId],
    },
    config: {
      enabled: !!candidateId,
    },
  });

  const handleOpenManager = useCallback(
    (tab?: CandidateManagerTabkeys) => {
      if (candidate)
        appDispatch({
          type: 'TOGGLE_CANDIDATE_MANAGER',
          payload: {
            isOpen: true,
            tab,
            id: candidate.id,
            enableNavigate: false,
          },
        });
    },
    [appDispatch, candidate]
  );
  const queryClient = useQueryClient();

  const refetch = useCallback(() => {
    queryClient.invalidateQueries({
      queryKey: [QueryKeys.getCandidate, candidate?.id],
      exact: false,
    });
  }, [candidate?.id, queryClient]);
  const isLoboxUser = !!candidate?.profile?.username;

  if (isLoading || parentLoading) return <CandidateDetailsSkeleton />;

  if (!candidate) return null;

  return (
    <>
      <CandidateCard
        avatar={candidate?.profile?.croppedImageUrl}
        firstText={candidate?.profile?.fullName}
        secondText={
          candidate?.profile?.usernameAtSign ?? candidate?.profile?.email?.value
        }
        thirdText={candidate.profile?.occupation?.label}
        fourthText={cleanRepeatedWords(
          candidate?.profile?.location?.title || ''
        )}
        treeDotMenu={<CandidateCardActions candidate={candidate} />}
        FirstTextWrapper={!isLoboxUser ? IsManualWrapper : undefined}
        footer={
          <Tabs
            activePath={selectedTab}
            onChangeTab={setSelectedTab}
            styles={{
              tabsRoot: classes.tabsRoot,
            }}
            tabs={tabs.map((tab) => ({ ...tab, title: t(tab.title) }))}
          />
        }
      >
        <HorizontalTagList
          tags={candidate.tags}
          title={t('candidate_tags')}
          editable
          onSuccess={refetch}
          apiFunc={(body) =>
            editCandidateAdditionalInfo({ candidateId: candidate.id, body })
          }
        />
        <Flex className={classes.badges}>
          <CardBadge
            value={candidate.notesCount}
            iconsDetails={{ iconName: 'note' }}
            tooltipProps={{
              children: t('notes'),
            }}
            onClick={() => handleOpenManager('notes')}
          />
          <CardBadge
            value={candidate.todosCount}
            iconsDetails={{ iconName: 'checklist' }}
            tooltipProps={{
              children: t('todos'),
            }}
            onClick={() => handleOpenManager('todos')}
          />
          <CardBadge
            value={candidate.meetingsCount}
            iconsDetails={{ iconName: 'meeting' }}
            tooltipProps={{
              children: t('meetings'),
            }}
            onClick={() => handleOpenManager('meetings')}
          />
          {!!candidate.lastModifiedDate && (
            <Flex className="ml-auto">
              <Tooltip
                trigger={
                  <DateView
                    className={classes.counterDate}
                    value={`${candidate.lastModifiedDate}`}
                  />
                }
              >
                <Typography
                  size={14}
                  font="400"
                  height={18}
                  color="tooltipText"
                >
                  {t('latest_activity')}
                </Typography>
              </Tooltip>
            </Flex>
          )}
        </Flex>
        <Flex className="!flex-row gap-12">
          {candidate.profile?.username ? (
            <SendMessageButton
              className="flex-1"
              disabled={!candidate.profile?.username}
              object={{
                id: candidate.profile.originalId,
                croppedImageUrl: candidate.profile.croppedImageUrl,
                fullName: candidate.profile.fullName,
                username: candidate.profile.username,
                isPage: false,
              }}
              fullWidth
            />
          ) : (
            <Button
              disabled
              className="flex-1"
              schema="semi-transparent"
              leftIcon="envelope"
              leftType="far"
              label={t('message')}
              fullWidth
            />
          )}
          <Button
            className="flex-1"
            label={t('manage')}
            leftIcon="user-cog"
            fullWidth
            onClick={() => handleOpenManager('notes')}
          />
        </Flex>
      </CandidateCard>
      <Panels candidate={candidate} tab={selectedTab} />
    </>
  );
};

export default CandidateDetails;

const Panels = ({
  tab,
  candidate,
}: BaseCandidateSectionProp & {
  tab: CandidateDetailsTabsNames;
}) => {
  switch (tab) {
    case 'jobs': {
      return <CandidateJobsTab candidate={candidate} />;
    }
    case 'availability': {
      return <CandidateAvailabilityTab candidate={candidate} />;
    }
    case 'similar': {
      return <CandidateSimilarTab candidate={candidate} />;
    }
    case 'activities': {
      return <CandidateActivitesTab candidate={candidate} />;
    }
    case 'insights': {
      return <CandidateInsightsTab candidate={candidate} />;
    }
    default: {
      return <CandidateAboutTab candidate={candidate} />;
    }
  }
};
