@import '/src/shared/theme/theme.scss';

.detailsRoot {
  padding-bottom: variables(xLargeGutter);
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
}

.tabsRoot {
  border-top: 1px solid colors(techGray_10);
}

.emptySearch {
  margin: 0;
}

.badges {
  flex-direction: row;
  align-items: center;
  gap: variables(gutter) * 0.5;
  .counterDate {
    font-size: variables(xLargeGutter) * 0.5;
    color: colors(secondaryDisabledText);
    margin-left: auto;
  }
  .bottomInfoRoot {
    padding: variables(xLargeGutter) * 0.25;
    border-radius: variables(gutter) * 0.25;
    border: 1px solid colors(techGray_20);
    flex-direction: row;
    align-items: center;
    width: fit-content;
    gap: variables(gutter) * 0.25;
    .bottomInfoIcon {
      width: variables(gutter);
      height: variables(gutter);
      justify-content: center;
      align-items: center;
    }
    & > span {
      line-height: variables(gutter) + 2;
    }
  }
}
