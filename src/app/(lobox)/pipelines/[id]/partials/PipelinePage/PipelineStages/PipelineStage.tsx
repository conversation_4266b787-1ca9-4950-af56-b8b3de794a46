/* eslint-disable react/no-unstable-nested-components */
import type { DroppableProvided } from '@hello-pangea/dnd';
import { Draggable } from '@hello-pangea/dnd';
import { getCandidateById } from '@shared/utils/api/candidates';
import DraggableContainer from '@shared/components/atoms/containers/DraggableContainer';
import StageCard from '@shared/components/atoms/containers/StageCard';
import CheckBox from '@shared/uikit/CheckBox';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { useState, useCallback, useMemo, useEffect, useRef, memo } from 'react';
import IconButton from '@shared/uikit/Button/IconButton';
import type { PipelineInfo } from '@shared/types/pipelineProps';
import useInfiniteQuery from 'shared/utils/hooks/useInfiniteQuery';
import type {
  NormalizedJobParticipationModel,
  PipelineStageFilter,
} from '@shared/types/jobsProps';
import { QueryKeys } from '@shared/utils/constants';
import {
  pinCandidate,
  searchPipelineParticipations,
} from 'shared/utils/api/jobs';

import Flex from '@shared/uikit/Flex';
import DividerVertical from '@shared/uikit/Divider/DividerVertical';
import Typography from '@shared/uikit/Typography';
import ViewPortList from 'shared/uikit/ViewPortList';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import { useSearchParams } from 'next/navigation';
import type { CandidateManagerTabkeys } from '@shared/components/Organism/CandidateManager/tabs';
import Tooltip from '@shared/uikit/Tooltip';
import { useMutation } from '@tanstack/react-query';
import StageMoreOptions from './PipelineStage/StageMoreOptions';
import EmptyStage from './PipelineStage/EmptyStage';
import BulkStageMenu from './PipelineStage/BulkStageMenu';
import { usePipelinePageCtx } from '../../PipelinePageProvider';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';

interface PipelineStageProps {
  stage: PipelineInfo;
  draggableProvided: DroppableProvided;
  isBulked?: boolean;
  onBulk: (id?: string) => void;
  wrapperRef: React.RefObject<HTMLDivElement>;
  innerFilter: PipelineStageFilter;
  setInnerFilter: (filter: PipelineStageFilter) => void;
  'data-stage-id'?: string;
  className?: string;
}

interface SelectedCandidatesProps {
  variant: 'all' | 'intermediate' | 'none';
  items: any[];
}

const Footer = (props: React.HTMLProps<HTMLDivElement>) => (
  <Flex {...props} className="gap-12">
    {new Array(20).fill(0).map((_, index) => (
      <StageCard.CardSkeleton key={index} />
    ))}
  </Flex>
);

Footer.displayName = 'Footer';

const PipelineStage: React.FunctionComponent<PipelineStageProps> = (props) => {
  const {
    stage,
    draggableProvided: provided,
    isBulked,
    onBulk,
    wrapperRef,
    innerFilter,
    setInnerFilter,
    'data-stage-id': dataStageId,
    className,
  } = props;
  const { t } = useTranslation();
  const { allParams, handleChangeParams } = useCustomParams();
  const { filter } = usePipelinePageCtx();
  const searchParams = useSearchParams();
  const currentEntityId = searchParams.get('currentEntityId');
  const appDispatch = useGlobalDispatch();
  const viewPortListRef = useRef<any>(null);

  const [selectedCandidates, setSelectedCandidates] =
    useState<SelectedCandidatesProps>({
      variant: 'none',
      items: [],
    });

  const {
    data: listData,
    fetchNextPage,
    totalElements,
    totalPages,
    isLoading,
    hasNextPage,
    refetch,
  } = useInfiniteQuery<NormalizedJobParticipationModel>(
    [QueryKeys.getPipelineParticipants, String(stage.id), filter, innerFilter],
    {
      func: (params: { page: number }) =>
        searchPipelineParticipations({
          id: stage.id,
          params: { page: params.page ?? 0, ...filter, ...innerFilter },
        }),
      size: 20,
    }
  );

  const { mutate: pinCandidateMutation } = useMutation({
    mutationFn: pinCandidate,
    onSuccess: () => refetch(),
  });

  const { mutate: getCandidate } = useMutation({
    mutationFn: (candidateId: string) => getCandidateById(candidateId!),
  });

  /* ------ Auto-scroll effect when currentEntityId changes or data loads ----- */
  useEffect(() => {
    if (
      currentEntityId &&
      listData &&
      listData?.length > 0 &&
      viewPortListRef.current
    ) {
      const targetIndex = listData?.findIndex(
        (item) =>
          item?.candidate?.id === currentEntityId ||
          item?.applicant?.id === currentEntityId
      );

      if (targetIndex !== -1) {
        setTimeout(() => {
          viewPortListRef.current?.scrollToIndex({
            index: targetIndex,
            align: 'center',
            behavior: 'smooth',
          });
        }, 100);
      }
    }
  }, [currentEntityId, listData]);

  useEffect(() => {
    if (allParams.pipelineRefetch === stage.type) {
      refetch();
      handleChangeParams({ remove: ['pipelineRefetch'] });
    }
  }, [allParams, stage]);

  useEffect(
    () => () => {
      refetch();
    },
    [refetch]
  );

  const hasActiveCandidate = useMemo(() => {
    if (stage.type !== 'CUSTOMIZE' && stage.type !== 'INTERVIEW') return false;
    return listData?.some((candidate) => !candidate.rejected);
  }, [listData, stage]);

  const onToggleBulk = useCallback(
    (id?: string) => {
      if (!id) {
        setSelectedCandidates({
          variant: 'none',
          items: [],
        });
      }
      onBulk(id);
    },
    [onBulk]
  );

  const rejectedList = useMemo(
    () => listData.filter((candidate) => candidate.rejected),
    [listData]
  );

  const nonRejectedCandidates = useMemo(
    () => listData.filter((candidate) => !candidate.rejected),
    [listData]
  );

  const onSelectAll = useCallback(() => {
    if (selectedCandidates.variant === 'all') {
      setSelectedCandidates({
        variant: 'none',
        items: [],
      });
    } else {
      // Select all non-rejected candidates
      const nonRejectedCandidateIds = nonRejectedCandidates.map(
        (candidate) => candidate.id
      );

      setSelectedCandidates({
        variant: 'all',
        items: nonRejectedCandidateIds,
      });
    }
  }, [selectedCandidates.variant, nonRejectedCandidates]);

  const onToggleItem = useCallback(
    (id: string) => {
      const selectedItemId = selectedCandidates.items.find(
        (userId) => userId === id
      );
      let items = [];

      if (selectedItemId) {
        items = selectedCandidates.items.filter(
          (userId) => userId !== selectedItemId
        );
      } else {
        items = [...selectedCandidates.items, id];
      }

      // Get count of non-rejected candidates for proper variant calculation
      const nonRejectedCount = listData.filter(
        (candidate) => !candidate.rejected
      ).length;

      setSelectedCandidates({
        variant:
          items.length === 0
            ? 'none'
            : items.length === nonRejectedCount
              ? 'all'
              : 'intermediate',
        items,
      });
    },
    [selectedCandidates.items, listData]
  );

  const handleOpenManagerModal = useCallback(
    (candidateId: string, tab?: CandidateManagerTabkeys) => {
      setDataId(candidateId);
      // handleChangeParams({ add: { currentEntityId: `${candidateId}` } });
      if (candidateId) {
        appDispatch({
          type: 'TOGGLE_CANDIDATE_MANAGER',
          payload: {
            isOpen: true,
            tab: 'notes',
            id: candidateId,
            enableNavigate: true,
          },
        });
      }
    },
    [appDispatch]
  );

  const setDataId = useCallback(
    (id: string) => {
      if (id) handleChangeParams({ add: { currentEntityId: id } });
    },
    [handleChangeParams]
  );

  const page =
    allParams.page && Number.isSafeInteger(+allParams.page)
      ? +allParams.page
      : 0;

  const setPageParam = useCallback(
    (p?: number) => {
      if (p) {
        handleChangeParams({
          add: { page: `${p}` },
          remove: ['currentEntityId'],
        });
      } else {
        handleChangeParams({
          remove: ['page'],
        });
      }
    },
    [handleChangeParams]
  );

  const handlePinClick = useCallback(
    (e: React.MouseEvent, userId: string, isPinned: boolean) => {
      e.stopPropagation();
      pinCandidateMutation({
        id: userId,
        pin: !isPinned,
      });
    },
    [pinCandidateMutation]
  );

  const renderItem = useCallback(
    (index: number, user: NormalizedJobParticipationModel) => {
      const isSelected =
        (currentEntityId &&
          user?.candidate?.id &&
          currentEntityId === user?.candidate?.id) ||
        (currentEntityId &&
          user?.applicant?.id &&
          currentEntityId === user?.applicant?.id);

      const candidateId = user?.candidate?.id || user?.applicant?.id;
      const labelColor = user.rejected
        ? 'error'
        : user.type === 'APPLICANT'
          ? 'brand'
          : 'pendingOrange';

      return (
        <Draggable key={`${user.id}`} draggableId={`${user.id}`} index={index}>
          {(dragProvided, dragSnapshot) => (
            <DraggableContainer
              ref={dragProvided.innerRef}
              {...dragProvided.dragHandleProps}
              {...dragProvided.draggableProps}
              className="w-full mb-12"
              snapshot={dragSnapshot}
            >
              <StageCard.Card
                key={`stage_card_applicant_${user.id}`}
                id={user?.candidate?.id || user?.applicant?.id}
                image={user.user.croppedImageUrl}
                fullName={user.user.fullName ?? ''}
                subtitle={user.user.occupationName ?? ''}
                date={user.dateTime}
                rate={user.avgScore}
                vendorSubmitted={user?.vendorSubmitted}
                collaboratorSubmitted={user?.collaboratorSubmitted}
                repeated={user?.repeated}
                automated={user.automated}
                notesCount={user.notesCount}
                todosCount={user.todosCount}
                meetingsCount={user.meetingsCount}
                className={isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : ''}
                data-candidate-id={candidateId}
                labelProps={{
                  children: t(user.rejected ? 'rejected' : user.type),
                  color: labelColor,
                }}
                action={
                  isBulked ? (
                    <Tooltip
                      placement="top"
                      hidden={!user.rejected}
                      triggerWrapperClassName="ml-auto"
                      trigger={
                        <CheckBox
                          // classNames={{ root: 'ml-auto' }}
                          defaultSchema="secondaryDisabledText"
                          onChange={() => onToggleItem(user.id)}
                          disabled={user.rejected}
                          value={selectedCandidates.items.includes(user.id)}
                        />
                      }
                    >
                      <Typography
                        size={13}
                        font="400"
                        color="skeletonBg"
                        className="max-w-[122px] text-center"
                      >
                        {t('rejected_candidate_cant_be_selected')}
                      </Typography>
                    </Tooltip>
                  ) : (
                    <IconButton
                      name="pin"
                      className="ml-auto"
                      colorSchema="secondaryDisabledText"
                      size="sm18"
                      type={user.pinned ? 'fas' : 'far'}
                      onClick={(e) => handlePinClick(e, user.id, user.pinned)}
                    />
                  )
                }
                onClick={() =>
                  isBulked
                    ? onToggleItem(user.id)
                    : handleOpenManagerModal(
                        user?.candidate?.id || user.applicant?.id,
                        'notes'
                      )
                }
              />
            </DraggableContainer>
          )}
        </Draggable>
      );
    },
    [
      isBulked,
      selectedCandidates.items,
      onToggleItem,
      t,
      currentEntityId,
      handlePinClick,
      handleOpenManagerModal,
    ]
  );

  const handleEndReached = useCallback(() => {
    if (hasNextPage) fetchNextPage();
  }, [hasNextPage, fetchNextPage]);

  return (
    <>
      <StageCard className="flex-1 relative" dataStageId={dataStageId}>
        <StageCard.Header
          title={stage.title ?? ''}
          badge={{
            color: stage.color ?? 'pendingOrange',
            count: stage.count,
          }}
          leftAction={
            isBulked && (
              <CheckBox
                classNames={{ root: 'mr-8' }}
                defaultSchema="secondaryDisabledText"
                onChange={onSelectAll}
                value={
                  selectedCandidates.variant === 'all' ||
                  selectedCandidates.variant === 'intermediate'
                }
                intermediate={selectedCandidates.variant === 'intermediate'}
              />
            )
          }
        >
          <StageMoreOptions
            stage={stage}
            onBulk={onToggleBulk}
            isBulked={isBulked}
            hasActiveCandidate={hasActiveCandidate}
            setFilter={setInnerFilter}
            filter={innerFilter}
          />
        </StageCard.Header>
        <StageCard.Content className="flex-1 overflow-y-hidden !p-0">
          {!isLoading && (
            <div
              {...provided.droppableProps}
              ref={provided.innerRef}
              id="cards-container"
              className="flex flex-1 flex-col overflow-y-hidden"
            >
              {listData.length ? (
                <ViewPortList
                  ref={viewPortListRef} // Add ref here
                  useRelativeScroller
                  style={{ height: '100%' }}
                  className="p-12 flex-1"
                  data={listData}
                  endReached={handleEndReached}
                  increaseViewportBy={100}
                  itemContent={renderItem}
                  components={{
                    List: Flex,
                    Footer: () =>
                      isLoading || hasNextPage ? <Footer /> : null,
                  }}
                />
              ) : (
                <EmptyStage stage={stage} />
              )}
            </div>
          )}
          {isLoading && (
            <div className="px-12 mt-12">
              <Footer />
            </div>
          )}
        </StageCard.Content>
        {isBulked && (
          <BulkStageMenu
            selectedCandidates={selectedCandidates.items}
            stage={stage}
            onCloseBulk={onToggleBulk}
            wrapperHeight={wrapperRef.current?.clientHeight}
            innerFilter={innerFilter}
          />
        )}
      </StageCard>
      {stage.type === 'HIRED' && (
        <Flex className="items-center gap-[28px]">
          <DividerVertical className="flex-1" />
          <div className="w-[18px] h-[120px] flex items-center justify-center">
            <Typography className="-rotate-90 whitespace-nowrap">
              {t('onboarding_stages')}
            </Typography>
          </div>
          <DividerVertical className="flex-1" />
        </Flex>
      )}
    </>
  );
};

export default memo(PipelineStage);
