import EmptySearchResult from 'shared/components/Organism/EmptySearchResult';
import SearchList from 'shared/components/Organism/SearchList';
import type {
  CandidateJobCardProps,
  SingleJobAPIProps,
} from 'shared/types/jobsProps';
import { useCallback, type FC } from 'react';
import useChangePipeline from 'shared/hooks/useChangePipeline';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { QueryKeys } from 'shared/utils/constants';
import { getJobCandidates } from '@shared/utils/api/jobs';
import Button from '@shared/uikit/Button';
import { openMultiStepForm } from '@shared/hooks/useMultiStepForm';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import classes from './RecruiterJobDetailsStyles.module.scss';
import JobCandidateCard from '@shared/components/molecules/JobCandidateCard/JobCandidateCard';
import usePaginateQuery from '@shared/utils/hooks/usePaginateQuery';
import { CandidateManagerTabkeys } from '@shared/components/Organism/CandidateManager/tabs';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';

interface RecruiterJobDetailsCandidatesProps {
  job: SingleJobAPIProps;
}

const RecruiterJobDetailsCandidates: FC<RecruiterJobDetailsCandidatesProps> = ({
  job,
}) => {
  const { t } = useTranslation();
  const appDispatch = useGlobalDispatch();
  const {
    content: data,
    totalElements,
    totalPages,
    isLoading,
    refetch,
  } = usePaginateQuery<CandidateJobCardProps>({
    action: {
      apiFunc: getJobCandidates,
      key: [QueryKeys.jobCandidates, job.id],
      params: {
        id: job.id,
        size: 20,
      },
    },
  });

  const { onChangePipeline } = useChangePipeline({
    onSuccess: refetch,
    variant: 'candidate',
  });

  const handleOpenManagerModal = useCallback(
    (candidateId: string, tab: CandidateManagerTabkeys) => {
      appDispatch({
        type: 'TOGGLE_CANDIDATE_MANAGER',
        payload: {
          isOpen: true,
          tab,
          id: candidateId,
          enableNavigate: false,
        },
      });
    },
    [appDispatch]
  );

  const handleOpenCandidatesLink = () =>
    openMultiStepForm({
      formName: 'linkJobForm',
      data: {
        id: job.id,
        target: 'job',
      },
    });

  return (
    <SearchList
      entity="recruiterJobs"
      isLoading={isLoading}
      totalElements={Number(totalElements)}
      data={data ?? []}
      onPageChange={() => {}}
      totalPages={Number(totalPages)}
      className={{ root: classes.applicantsRoot }}
      renderItem={(candidate) => (
        <JobCandidateCard
          data={candidate}
          showActions
          onChangePipeline={(pipelineId) =>
            onChangePipeline({
              userId: candidate.id,
              pipelineId: pipelineId as string,
            })
          }
          variant="candidate"
          showTags
          key={`candidate_${candidate.id}`}
          onPrimaryButtonClick={() => {
            handleOpenManagerModal(candidate.candidate.id, 'notes');
          }}
        />
      )}
      emptyList={
        <EmptySearchResult
          title={t('no_candidate')}
          sectionMessage={translateReplacer(t('no_antity_in_object'), [
            t('candidates').toLowerCase(),
            t('link_candidate').toLowerCase(),
          ])}
          className={classes.emptyResult}
          classNames={{ description: '!mt-12' }}
        >
          <Button
            label={t('link_candidate')}
            className="mt-20"
            leftIcon="link-rotate"
            leftType="far"
            onClick={handleOpenCandidatesLink}
          />
        </EmptySearchResult>
      }
      // parentPage={0}
      noItemButtonAction
      innerList
    />
  );
};

export default RecruiterJobDetailsCandidates;
