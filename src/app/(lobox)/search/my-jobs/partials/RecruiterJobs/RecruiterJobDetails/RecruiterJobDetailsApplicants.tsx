import ApplicantCard from 'shared/components/molecules/ApplicantCard';
import EmptySearchResult from 'shared/components/Organism/EmptySearchResult';
import SearchList from 'shared/components/Organism/SearchList';
import type {
  ApplicationProps,
  SingleJobAPIProps,
} from 'shared/types/jobsProps';
import { useCallback, type FC } from 'react';
import useChangePipeline from 'shared/hooks/useChangePipeline';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { QueryKeys } from 'shared/utils/constants';
import { getJobApplications } from '@shared/utils/api/jobs';
import Button from '@shared/uikit/Button';
import useRecruiterJobMoreActions from '@shared/hooks/useRecruiterJobMoreActions';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import classes from './RecruiterJobDetailsStyles.module.scss';
import JobCandidateCard from '@shared/components/molecules/JobCandidateCard/JobCandidateCard';
import useReactInfiniteQuery from '@shared/utils/hooks/useInfiniteQuery';
import { CandidateManagerTabkeys } from '@shared/components/Organism/CandidateManager/tabs';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';

interface RecruiterJobDetailsApplicantsProps {
  job: SingleJobAPIProps;
}

const RecruiterJobDetailsApplicants: FC<RecruiterJobDetailsApplicantsProps> = ({
  job,
}) => {
  const { t } = useTranslation();
  const appDispatch = useGlobalDispatch();
  const { onAction } = useRecruiterJobMoreActions({
    exclude: ['submit_to_vendor'],
  });

  const { data, isLoading, totalElements, refetch, totalPages } =
    useReactInfiniteQuery<ApplicationProps>(
      [QueryKeys.jobApplications, job.id],
      {
        func: getJobApplications,
        size: 10,
        extraProps: {
          id: job.id,
        },
      }
    );

  const { onChangePipeline } = useChangePipeline({
    onSuccess: refetch,
    variant: 'applicant',
  });

  const handleOpenManagerModal = useCallback(
    (candidateId: string, tab: CandidateManagerTabkeys) => {
      appDispatch({
        type: 'TOGGLE_CANDIDATE_MANAGER',
        payload: {
          isOpen: true,
          tab,
          id: candidateId,
          enableNavigate: false,
        },
      });
    },
    [appDispatch]
  );

  const handleShareJob = () => onAction('share', job);

  return (
    <SearchList
      entity="recruiterJobs"
      isLoading={isLoading}
      totalElements={Number(totalElements)}
      data={data ?? []}
      onPageChange={() => {}}
      totalPages={Number(totalPages)}
      className={{ root: classes.applicantsRoot }}
      renderItem={(application) => (
        <JobCandidateCard
          data={application}
          showActions
          variant="applicant"
          showTags
          onChangePipeline={(pipeline) =>
            onChangePipeline({
              userId: application.id,
              pipelineId: pipeline.id as string,
            })
          }
          key={`application_${application.id}`}
          onPrimaryButtonClick={() => {
            handleOpenManagerModal(application.applicant.id, 'notes');
          }}
        />
      )}
      emptyList={
        <EmptySearchResult
          sectionMessage={translateReplacer(t('no_antity_in_object'), [
            t('applicants').toLowerCase(),
            t('share_job').toLowerCase(),
          ])}
          className={classes.emptyResult}
          classNames={{ description: '!mt-12' }}
        >
          <Button
            label={t('share_job')}
            className="mt-20"
            leftIcon="share"
            leftType="far"
            onClick={handleShareJob}
          />
        </EmptySearchResult>
      }
      // parentPage={0}
      noItemButtonAction
      innerList
    />
  );
};

export default RecruiterJobDetailsApplicants;
