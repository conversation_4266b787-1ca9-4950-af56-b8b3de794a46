'use client';

import * as Sentry from '@sentry/nextjs';
import isDev from '@shared/utils/constants/isDev';
import React, { useEffect } from 'react';
import ErrorBoundaryFallback from 'shared/uikit/ErrorBoundary/ErrorBoundary.fallback';

export default function Error({
  error,
  reset,
}: {
  error: Error;
  reset: () => void;
}) {
  const reportSentry = async (er: Error) => {
    if (isDev) {
      console.error('Sentry skipped in development:', er);
    } else {
      console.warn('🚀🚀🚀 ~ reportSentry ~ error', er);
      await Sentry.captureException(er);
    }
  };

  useEffect(() => {
    // Log the error to an error reporting service
    console.error('TOP LEVEL ERROR', error);
    reportSentry(error);
  }, [error]);

  return <ErrorBoundaryFallback error={error} reset={reset} />;
}
