'use client';

import * as Sentry from '@sentry/nextjs';
import React, { useEffect } from 'react';
import Providers from 'shared/providers';
import ErrorBoundaryFallback from 'shared/uikit/ErrorBoundary/ErrorBoundary.fallback';
import isDev from '@shared/utils/constants/isDev';

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  const reportSentry = async (errorObj: Error) => {
    if (isDev) {
      console.error('Sentry skipped in development:', errorObj);
    } else {
      console.warn('🚀🚀🚀 ~ reportSentry ~ error', errorObj);
      await Sentry.captureException(errorObj);
    }
  };

  useEffect(() => {
    // Log the error to an error reporting service
    console.error('TOP LEVEL ERROR', error);
    reportSentry(error);
  }, [error]);

  return (
    <html>
      <body>
        <Providers lng="en">
          {isDev ? (
            <>
              <h2>Development Mode Error Details</h2>
              <pre>{error?.message}</pre>
              <pre>{error?.stack}</pre>
            </>
          ) : (
            <p>
              We encountered an unexpected error. Our team has been notified.
            </p>
          )}
          <ErrorBoundaryFallback error={error} reset={reset} />
        </Providers>
      </body>
    </html>
  );
}
