# 🧠 About the Project

**Lobox** is a social network web app built with [Next.js](https://nextjs.org/) using the new **App Router** architecture. This frontend project communicates with a backend API and provides a dynamic, fast, and scalable experience for users to create pages, posts, and explore features across the platform.

The **App Router** in Next.js enables powerful routing, layouts, and server components with file-based routing under the `app/` directory. Learn more here: [https://nextjs.org/docs/app](https://nextjs.org/docs/app)

The project follows a comprehensive set of coding standards and best practices to ensure maintainability, scalability, and performance.

For detailed coding guidelines and best practices, see [Code Guide & Conventions](./docs/style-guid/readme.md).

# 🚀 Tech Stack

#### Core Technologies

- **Framework**: [Next.js](https://nextjs.org/) 15.3.3 ([React](https://react.dev/) 19.1.0)
- **Language**: TypeScript 5
- **Styling**:
  - [Tailwind CSS](https://tailwindcss.com/) 3.4.14 (preferred)
  - Material-UI 6.1.2
  - Emotion (CSS-in-JS)
  - SASS/SCSS

#### State Management & Data Fetching

- **React Query**: [@tanstack/react-query](https://tanstack.com/query/latest) for api calling
- **React Store**: [@tanstack/react-store](https://tanstack.com/store/latest) for client state
- **Form Handling**: [Formik](https://formik.org/) with [Yup](https://github.com/jquense/yup) validation

#### UI Components & Animation

- **Component Library**: Custom UIKit with Material-UI
- **Animation**: [Framer Motion](https://www.framer.com/motion/) 10.16.16
- **Icons**: Icomoon React + SVGR
- **Charts**: Recharts

#### Development Tools

- **Linting**: ESLint with Airbnb configuration
- **Code Formatting**: Prettier
- **Testing**: Cypress, Testing Library
- **Storybook**: For component development
- **Bundle Analysis**: Webpack Bundle Analyzer

#### Additional Libraries

- **Internationalization**: [i18next](https://www.i18next.com/)
- **Image Processing**: Sharp, React Image File Resizer
- **Maps**: React Leaflet
- **File Upload**: React Dropzone
- **Rich Text**: [React Quill](https://github.com/zenoamaro/react-quill)
- **Utilities**: Lodash, Date-fns, Moment.js, Dayjs
- **Error tracking**: [Sentry](https://sentry.io/)

See more dependencies in [`package.json`](./package.json)

# 🧭 Getting Started

## 🛠 Requirements

Make sure your system has the following:

- **Operating System:** macOS or Linux
  (Windows is not recommended due to potential compatibility issues and time-consuming fixes)
- **Node.js:** `v20.16.0`
- **pnpm:** `v9.x.x`

## 🚪 Account Setup

1. Visit [https://dev.lobox.com](https://dev.lobox.com)
2. Register an account **with email and password only** (no social logins)
3. Complete your profile by:
   - Creating a **post**
   - Creating a **page**
   - Exploring various features of the site

## 📦 Project Setup

1. Clone the repo:

   ```bash
   git clone https://git.lobox.com/frontend/lobox-frontend.git
   cd lobox-frontend
   ```

2. Get the `.env` keys from the lead frontend developer and add them to a `.env` file in the root directory.

3. Install dependencies and start the development server:

   ```bash
   pnpm install
   pnpm dev
   ```

4. Open [http://localhost:3005](http://localhost:3005) in your browser.

# 🗂 Project Structure

The root directory contains a `src/` folder, which includes:

- `app/`: Contains all route-related files based on Next.js **App Router**.
- `shared/`: Contains shared files and modules used across different routes.

  We follow the **Atomic Design** methodology to organize UI components by levels of abstraction (Atoms, Molecules, Organisms, etc.).

To get familiar with the codebase:

- Run the project locally and explore which files are loaded per route.
- Inspect how pages are composed from smaller building blocks.
- If anything is unclear, feel free to ask the lead frontend developer.

## Folder Structure & Organization

### Recommended Structure

```
src/
├── app/                          # Next.js App Router
│   ├── (landing)/               # Route groups
│   │   ├── auth/
│   │   ├── home/
│   │   └── ...
│   ├── (lobox)/
│   │   ├── profile/
│   │   ├── network/
│   │   └── ...
│   ├── layout.tsx               # Root layout
│   ├── page.tsx                 # Home page
│   └── globals.css              # Global styles
├── shared/                      # Shared code
│   ├── components/              # Reusable components
│   │   ├── atoms/               # Basic UI elements
│   │   ├── molecules/           # Component combinations
│   │   ├── organisms/           # Complex components
│   │   ├── templates/           # Page templates
│   │   └── layouts/             # Layout components
│   ├── contexts/                # React contexts
│   ├── hooks/                   # Custom hooks
│   ├── utils/                   # Utility functions
│   │   ├── api/                 # API services
│   │   ├── hooks/               # Custom hooks
│   │   └── locales/             # Translations files
│   ├── types/                   # TypeScript type definitions
│   ├── constants/               # Constants and configuration
│   ├── svg/                     # Some svg icons
│   ├── theme/                   # Theme and styling
│   └── uikit/                   # Design system
└── middleware                   # Nextjs middleware
```

# 🛠 Development Guide

We follow a clean and structured development workflow. Please read this section carefully before starting your first task.

## 🔁 Git Flow & Pull Requests

- We use the **Git Flow** approach. Read more: [Git Flow Cheatsheet](https://danielkummer.github.io/git-flow-cheatsheet/)
- Alternatively, you can start a branch directly from the **Jira** task using the "Create Branch" button in the task detail.
- Always create a PR for each task. It helps with code review and better tracking.

#### Git Workflow Commands

```bash
# Feature branch workflow
git checkout develop
git pull origin develop
git checkout -b YOUR_BRANCH_NAME_FOLLOWING_JIRA_ID

# Development
git add .
git commit -m "feat: add user authentication components"

# Push and create pull request
git push origin -u
```

## ✅ PR Best Practices & Code Review Checklist

### PR Best Practices

- Ensure the **linter** is enabled and working correctly.
- Match the code style and conventions already used in the project.
- We follow the **Airbnb style guide**. It's recommended to review it before starting: [Airbnb React/JSX Style Guide](https://airbnb.io/javascript/react/)
- Avoid unrelated changes in your PR. If needed, split unrelated work into separate PRs.
- You can style using **Tailwind CSS** or **CSS Modules**.
- Always write **TypeScript**-safe code. Fixing type errors in unrelated files is encouraged and welcome.
- Once your task is done and the code is ready for testing, create the PR and inform the lead frontend developer.

### Code Review Checklist

- [ ] Follows Airbnb React conventions
- [ ] TypeScript types are properly defined
- [ ] Performance considerations are addressed
- [ ] SOLID principles are followed
- [ ] Clean code rules are followed
- [ ] Error handling is implemented
- [ ] Tests are written and passing
- [ ] Documentation is updated
- [ ] Security vulnerabilities are addressed

## 🚀 After PR Approval

- Your PR will be reviewed and merged by the lead frontend developer.
- Once deployed, **double-check** your feature on the `dev` environment to ensure everything works correctly.
- Congratulations — you've made your first contribution to Lobox! We hope to see many more.

## 📌 Jira Access

We manage tasks using Jira:
[https://lobox.atlassian.net/](https://lobox.atlassian.net/)

## 📋 Jira Workflow

Due to the size of our project, we manage multiple Jira projects. Inside each project, we organize work using **Stories** and **Tasks**. Here's how you should work with Jira:

1. **Filter your assigned tasks** to only see what's relevant to you.
2. When a task is assigned to you:
   - If it's clear and you're ready to work on it, move it to **`To Do`**.
   - If it needs clarification, keep it in **`Open`** and reach out to **Burhan**, the designer, or other developers to get clarity.
3. While working on a task, move it to **`In Progress`**.
4. If you're blocked and cannot continue, move it to **`Pending`**.
5. After you create the Pull Request, move the task to **`Deploy`**.
6. Once it is deployed and you've tested it yourself, move it to **`Test`** so QA can review.
7. QA will either:
   - Approve it and move it to **`Done`**
   - Or return it with comments, in which case it will go back to **`In Progress`**

⚠️ Do **not** move tasks to `Storage` or `Icebox` without explicit permission from **Burhan** or the lead frontend developer.

## 📡 API Documentation

We use **Swagger** for backend API documentation.
Please contact the backend developers to get the latest API endpoint documentation and any recent updates.

### 🚀 Deployment

Deployment to `development` and `production` environments is handled by the **DevOps team**.
You do not need to manually deploy builds to these environments.

### Development Build Commands

```bash
# Development build
pnpm run dev

# Production build
pnpm run build

# Start production server
pnpm start

# Analyze bundle size
pnpm run analyze
```

#### Testing Commands

```bash
# Run all tests
pnpm test

# Run tests in watch mode
pnpm run test:watch

# Run tests with coverage
pnpm run test:coverage

# Run E2E tests
pnpm run test:e2e

# Run Storybook
pnpm run storybook

# Build Storybook
pnpm run build-storybook
```

# 📚 Documentation & Guidelines

For comprehensive coding guidelines, best practices, and detailed documentation, please refer to the sections below and our [Code Guide & Conventions](./docs/style-guid/readme.md).

### Conclusion

By following these conventions and best practices, we ensure consistency across the codebase and deliver a better user experience.

**Key Takeaways:**

1. **Follow Airbnb React conventions** with TypeScript adaptations
2. **Implement SOLID principles** for better software architecture
3. **Write clean, readable code** with meaningful names and proper structure
4. **Use React design patterns** for better component organization
5. **Maintain security best practices** throughout development
6. **Write comprehensive tests** to ensure code quality
7. **Use proper development workflows** for team collaboration

Remember that these guidelines are living documents and should be updated as the project evolves and new best practices emerge.

---

_Last Updated: 4 August 2025_
_Version: 1.0_
